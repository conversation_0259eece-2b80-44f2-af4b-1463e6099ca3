import pytest
from unittest.mock import Mock, patch
from assertpy import assert_that
from jobs.quant_price_performance.main import (
    QuantPricePerformanceJob,
    QuantPricePerformance,
)


class TestQuantPricePerformanceJob:

    @pytest.fixture
    def job(self):
        return QuantPricePerformanceJob()

    def test_filter_with_valid_data(self, job):
        # Arrange
        input_data = {
            "YTD": {"medeze": "10.5", "helth": "8.2", "set": "5.1"},
            "P/E (X)": {"medeze": "15.2", "helth": "12.8", "set": "10.5"},
            "Other": {"medeze": "value1", "helth": "value2", "set": "value3"},
        }

        # Act
        result = job.filter(input_data)

        # Assert
        assert_that(result).is_equal_to(
            {
                "YTD": {"medeze": "10.5", "helth": "8.2", "set": "5.1"},
                "P/E (X)": {"medeze": "15.2", "helth": "12.8", "set": "10.5"},
            }
        )

    def test_filter_with_missing_keys(self, job):
        # Arrange
        input_data = {
            "YTD": {"medeze": "10.5", "helth": "8.2", "set": "5.1"},
            "Other": {"medeze": "value1", "helth": "value2", "set": "value3"},
        }

        # Act & Assert
        with pytest.raises(ValueError) as exc_info:
            job.filter(input_data)

        assert_that(str(exc_info.value)).is_equal_to(
            "Missing required keys in data: P/E (X)"
        )

    def test_filter_with_empty_data(self, job):
        # Arrange
        input_data = {}

        # Act & Assert
        with pytest.raises(ValueError) as exc_info:
            job.filter(input_data)

        assert_that(str(exc_info.value)).is_equal_to(
            "Missing required keys in data: YTD, P/E (X)"
        )

    def test_transform_with_valid_data(self, job):
        # Arrange
        input_data = {
            "YTD": {"medeze": "10.5", "helth": "8.2", "set": "5.1"},
            "P/E (X)": {"medeze": "15.2", "helth": "12.8", "set": "10.5"},
        }

        # Act
        result = job.transform(input_data)

        # Assert
        assert_that(result).is_instance_of(QuantPricePerformance)
        assert_that(result.ytd_medeze).is_equal_to(10.5)
        assert_that(result.ytd_helth).is_equal_to(8.2)
        assert_that(result.ytd_set).is_equal_to(5.1)
        assert_that(result.pe_medeze).is_equal_to(15.2)
        assert_that(result.pe_helth).is_equal_to(12.8)
        assert_that(result.pe_set).is_equal_to(10.5)
