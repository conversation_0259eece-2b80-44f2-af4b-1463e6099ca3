import pytest
import pendulum
import json
import datetime
from assertpy import assert_that
from lib.scraper.apify.transformer import (
    FacebookPostOutputTransformer,
    FacebookCommentOutputTransformer,
    InstagramPostOutputTransformer,
    InstagramCommentOutputTransformer,
    YoutubePostOutputTransformer,
    YoutubeCommentOutputTransformer,
    TwitterOutputTransformer,
    TikTokPostOutputTransformer,
    TikTokCommentOutputTransformer,
    get_transformer,
)


class TestFacebookTransformers:
    def test_facebook_post_transformer(self):
        # Arrange
        transformer = FacebookPostOutputTransformer()
        input_data = {
            "post_id": "1518480306272096",
            "type": "post",
            "url": "https://www.facebook.com/HoonSmart/posts/pfbid02gssuZWUnVVYsdEEzR6WsBYjdBYjq3p4WKvQrVNMQh94i2PG9ZLtBjtXASF7Bpfn7l",
            "message": '📌MEDEZE สยายปีก "เซลล์ต้นกำเนิด" ...ประกาศซื้อ Cordlife สิงคโปร์ 10%\n.\n#MEDEZE ประกาศซื้อหุ้น Cordlife ธุรกิจเก็บสเต็มเซลล์ชื่อดังของสิงค์โปร์ 10% มูลค่า 165 ล้านบาท  คาดรู้ผลคำเสนอซื้อหุ้นจากนักลงทุนรายย่อยมิ.ย.นี้ " นพ.วีรพล เขมะรังสรรค์" ซีอีโอ ชี้ช่วยเสริมแกร่งและจุดแข็งของ 2 บริษัท ขยายธุรกิจให้บริการ "เซลล์ต้นกำเนิด" ระดับภูมิภาค \n.\n#HoonSmart  #หุ้นสมาร์ท',
            "timestamp": 1747135177,
            "comments_count": 1,
            "reactions_count": 35,
            "reshare_count": 2,
            "reactions": {
                "angry": 1,
                "care": 2,
                "haha": 3,
                "like": 14,
                "love": 4,
                "sad": 5,
                "wow": 6,
            },
            "author": {
                "id": "100043304236122",
                "name": "หุ้นสมาร์ท - HoonSmart : เสนอความจริง ทุกการลงทุน",
                "url": "https://www.facebook.com/HoonSmart",
                "profile_picture_url": "https://scontent.fsgn19-1.fna.fbcdn.net/v/t39.30808-1/304855992_764627581657376_4976676663465446699_n.jpg?stp=cp0_dst-jpg_s80x80_tt6&_nc_cat=108&ccb=1-7&_nc_sid=2d3e12&_nc_ohc=RvY_xhKkCG0Q7kNvwF6msjY&_nc_oc=AdkjsIf_YJYWaF1KsrCVNQbWT2Jwplj94DDz8PObgAJz30eYtNyLL5-6RlovfogbQV4&_nc_zt=24&_nc_ht=scontent.fsgn19-1.fna&_nc_gid=R4xIxKjR4rcuYdyvi6EuBw&oh=00_AfJA1xAPhEDE4MLxAdqImH9T1uHKrtVeDhdo_wk2dIWVZA&oe=6845B825",
            },
            "image": "https://scontent.fsgn19-1.fna.fbcdn.net/v/t39.30808-6/497497938_1518577576262369_1550622471242374210_n.jpg?_nc_cat=106&ccb=1-7&_nc_sid=833d8c&_nc_ohc=ujj5-5bDUzwQ7kNvwEGHuol&_nc_oc=AdngDERjtCj89xpA5-lKCJrHJnembyF9AGKWjHYVnya7HrlqldxaHhiXb4_vRlH4BZY&_nc_zt=23&_nc_ht=scontent.fsgn19-1.fna&_nc_gid=R4xIxKjR4rcuYdyvi6EuBw&oh=00_AfLASjHYsWe_60ZmzNASSwI9moiuGqxoZ6eqYikgB_li7g&oe=6845B9DE",
            "video": None,
            "album_preview": None,
            "video_files": None,
            "video_thumbnail": None,
            "external_url": None,
            "attached_event": None,
            "attached_post": None,
            "attached_post_url": None,
            "search_term": "medeze",
        }

        # Act
        result = transformer.transform(input_data)

        # Assert
        expected_output = {
            "id": "1518480306272096",
            "timestamp": pendulum.datetime(2025, 5, 13, 11, 19, 37, tz="UTC"),
            "content": '📌MEDEZE สยายปีก "เซลล์ต้นกำเนิด" ...ประกาศซื้อ Cordlife สิงคโปร์ 10% . #MEDEZE ประกาศซื้อหุ้น Cordlife ธุรกิจเก็บสเต็มเซลล์ชื่อดังของสิงค์โปร์ 10% มูลค่า 165 ล้านบาท  คาดรู้ผลคำเสนอซื้อหุ้นจากนักลงทุนรายย่อยมิ.ย.นี้ " นพ.วีรพล เขมะรังสรรค์" ซีอีโอ ชี้ช่วยเสริมแกร่งและจุดแข็งของ 2 บริษัท ขยายธุรกิจให้บริการ "เซลล์ต้นกำเนิด" ระดับภูมิภาค  . #HoonSmart  #หุ้นสมาร์ท',
            "url": "https://www.facebook.com/HoonSmart/posts/pfbid02gssuZWUnVVYsdEEzR6WsBYjdBYjq3p4WKvQrVNMQh94i2PG9ZLtBjtXASF7Bpfn7l",
            "author_name": "หุ้นสมาร์ท - HoonSmart : เสนอความจริง ทุกการลงทุน",
            "author_id": "100043304236122",
            "hashtags": ["#MEDEZE", "#HoonSmart", "#หุ้นสมาร์ท"],
            "like_count": 14,
            "love_count": 4,
            "wow_count": 6,
            "sad_count": 5,
            "angry_count": 1,
            "haha_count": 3,
            "comment_count": 1,
            "reaction_count": 35,
            "engagement_count": 38,
            "share_count": 2,
        }
        assert_that(result).contains_key(
            "id",
            "timestamp",
            "content",
            "url",
            "author_name",
            "author_id",
            "hashtags",
            "like_count",
            "love_count",
            "wow_count",
            "sad_count",
            "angry_count",
            "haha_count",
            "comment_count",
            "reaction_count",
            "engagement_count",
            "share_count",
        )
        assert_that(result["id"]).is_equal_to("1518480306272096")
        assert_that(result["content"]).is_equal_to(
            '📌MEDEZE สยายปีก "เซลล์ต้นกำเนิด" ...ประกาศซื้อ Cordlife สิงคโปร์ 10% . #MEDEZE ประกาศซื้อหุ้น Cordlife ธุรกิจเก็บสเต็มเซลล์ชื่อดังของสิงค์โปร์ 10% มูลค่า 165 ล้านบาท  คาดรู้ผลคำเสนอซื้อหุ้นจากนักลงทุนรายย่อยมิ.ย.นี้ " นพ.วีรพล เขมะรังสรรค์" ซีอีโอ ชี้ช่วยเสริมแกร่งและจุดแข็งของ 2 บริษัท ขยายธุรกิจให้บริการ "เซลล์ต้นกำเนิด" ระดับภูมิภาค  . #HoonSmart  #หุ้นสมาร์ท'
        )
        assert_that(result["hashtags"]).is_equal_to(
            ["#MEDEZE", "#HoonSmart", "#หุ้นสมาร์ท"]
        )
        assert_that(result["author_name"]).is_equal_to(
            "หุ้นสมาร์ท - HoonSmart : เสนอความจริง ทุกการลงทุน"
        )
        assert_that(result["timestamp"]).is_equal_to(
            pendulum.datetime(2025, 5, 13, 11, 19, 37, tz="UTC")
        )
        assert_that(result["author_id"]).is_equal_to("100043304236122")
        assert_that(result["like_count"]).is_equal_to(14)
        assert_that(result["love_count"]).is_equal_to(4)
        assert_that(result["wow_count"]).is_equal_to(6)
        assert_that(result["sad_count"]).is_equal_to(5)
        assert_that(result["angry_count"]).is_equal_to(1)
        assert_that(result["haha_count"]).is_equal_to(3)
        assert_that(result["comment_count"]).is_equal_to(1)
        assert_that(result["reaction_count"]).is_equal_to(35)
        assert_that(result["engagement_count"]).is_equal_to(38)
        assert_that(result["share_count"]).is_equal_to(2)

        assert_that(result).is_equal_to(expected_output)

    def test_facebook_comment_transformer(self):
        transformer = FacebookCommentOutputTransformer()
        input_data =     {
            "facebookUrl": "https://www.facebook.com/groups/330668108829016/posts/1118792300016589/",
            "commentUrl": "https://www.facebook.com/groups/330668108829016/posts/1118792300016589/?comment_id=1118800886682397",
            "id": "Y29tbWVudDoxMTE4NzkyMzAwMDE2NTg5XzExMTg4MDA4ODY2ODIzOTc=",
            "feedbackId": "ZmVlZGJhY2s6MTExODc5MjMwMDAxNjU4OV8xMTE4ODAwODg2NjgyMzk3",
            "date": "2025-04-30T02:44:15.000Z",
            "text": "พักอยู่อ้อมน้อยขอข้อมูลเพิ่มเติม",
            "profileUrl": "https://www.facebook.com/prasit.kh.2024",
            "profilePicture": "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.30808-1/495018185_566934453102168_4166917130500487266_n.jpg?stp=cp0_dst-jpg_s32x32_tt6&_nc_cat=103&ccb=1-7&_nc_sid=e99d92&_nc_ohc=lKDVkNAaixEQ7kNvwG34fBc&_nc_oc=AdncXgPXgoW21BqEUpOjFmc942TTsr4aDZuFUPNSYH9BMed0GK-kwiHovDyKId-ncsA&_nc_zt=24&_nc_ht=scontent-dfw5-1.xx&_nc_gid=SSrlNyOmYONHoAm8nJA_Ow&oh=00_AfEc03DwnNjuIXtcqQrLEdaiITakHXH31YpJLok5wCRXaQ&oe=68194D83",
            "profileId": "pfbid02iHz6rLFmr4A83XUjdMknXdmjSi27jPRFhGg9dxGLMJN7XaVcLp8EmjSLJLcManZSl",
            "profileName": "Prasit Kh",
            "likesCount": 7,
            "commentsCount": 3,
            "comments": [
                {
                    "commentUrl": "https://www.facebook.com/groups/330668108829016/posts/1118792300016589/?comment_id=1118800886682397&reply_comment_id=1118806730015146",
                    "id": "Y29tbWVudDoxMTE4NzkyMzAwMDE2NTg5XzExMTg4MDY3MzAwMTUxNDY=",
                    "feedbackId": "ZmVlZGJhY2s6MTExODc5MjMwMDAxNjU4OV8xMTE4ODA2NzMwMDE1MTQ2",
                    "date": "2025-04-30T02:55:27.000Z",
                    "text": "ขอเบอร์ติดต่อกลับค่ะ",
                    "profileUrl": "https://www.facebook.com/people/HR-Medeze/pfbid02T5EseAjTqJryotufB7Pb6qScmPTasfS6PXfJ2BW1eYWeHPpUPujKPH52Ds4Vrqz6l/",
                    "profilePicture": "https://scontent-dfw5-3.xx.fbcdn.net/v/t1.6435-1/96754691_103286691386989_4280781117805559808_n.jpg?stp=cp0_dst-jpg_s32x32_tt6&_nc_cat=108&ccb=1-7&_nc_sid=e99d92&_nc_ohc=uNgt7BY3D8sQ7kNvwHOjk6T&_nc_oc=Adm7mjzTj26HIOaA9s3_oVcUsqCwhVOlwAzzYgaUSA5s6C3gNuORjNE_JmgAtIjZySc&_nc_zt=24&_nc_ht=scontent-dfw5-3.xx&_nc_gid=SSrlNyOmYONHoAm8nJA_Ow&oh=00_AfFWDQJM9FGXzKB97bqMrN1APJpYAPwDiOa7XXQO9nVepA&oe=683AF7C4",
                    "profileId": "pfbid02T5EseAjTqJryotufB7Pb6qScmPTasfS6PXfJ2BW1eYWeHPpUPujKPH52Ds4Vrqz6l",
                    "profileName": "HR Medeze",
                    "likesCount": "0",
                    "commentsCount": 2,
                    "threadingDepth": 1,
                    "replyToCommentId": "Y29tbWVudDoxMTE4NzkyMzAwMDE2NTg5XzExMTg4MDA4ODY2ODIzOTc=",
                    "parentComment": {
                        "author": {
                            "__typename": "User",
                            "name": "Prasit Kh",
                            "short_name": "Prasit",
                            "id": "pfbid02iHz6rLFmr4A83XUjdMknXdmjSi27jPRFhGg9dxGLMJN7XaVcLp8EmjSLJLcManZSl"
                        },
                        "id": "Y29tbWVudDoxMTE4NzkyMzAwMDE2NTg5XzExMTg4MDA4ODY2ODIzOTc="
                    },
                    "parentReply": {
                        "author": {
                            "__typename": "User",
                            "name": "Prasit Kh",
                            "gender": "MALE",
                            "id": "pfbid02iHz6rLFmr4A83XUjdMknXdmjSi27jPRFhGg9dxGLMJN7XaVcLp8EmjSLJLcManZSl"
                        },
                        "id": "Y29tbWVudDoxMTE4NzkyMzAwMDE2NTg5XzExMTg4MDA4ODY2ODIzOTc="
                    }
                }
            ],
            "threadingDepth": 0,
            "facebookId": "1118792300016589",
            "postTitle": "บริษัท เมดีซ กรุ๊ป จำกัด (มหาชน)\n\nประเภทธุรกิจ : ทางการแพทย์ จัดเก็บสเต็มเซลล์\n\nรับสมัครงานตำแหน่ง เจ้าหน้าที่ยานยนต์\n\nสถานที่ปฎิบัติงาน : ซอยกระทุ่มล้ม 18 พุทธมณฑลสาย4  นครปฐม \n\nคุณสมบัติ\n\n- เพศชาย...",
            "groupTitle": "หางาน สามพราน อ้อมน้อย อ้อมใหญ่ สาย5 เทียนดัด กระทุ่มแบน",
            "inputUrl": "https://www.facebook.com/groups/330668108829016/posts/1118792300016589/"
        }

        # Act
        result = transformer.transform(input_data)

        # Assert
        expected_output = {
            "id": "Y29tbWVudDoxMTE4NzkyMzAwMDE2NTg5XzExMTg4MDA4ODY2ODIzOTc=",
            "timestamp": pendulum.datetime(2025, 4, 30, 2, 44, 15, tz="UTC"), 
            "parent_comment_id": None,
            "content": "พักอยู่อ้อมน้อยขอข้อมูลเพิ่มเติม",
            "url": "https://www.facebook.com/groups/330668108829016/posts/1118792300016589/?comment_id=1118800886682397",
            "like_count": 7,
            "reply_count": 3,
            "engagement_count": 7,
            "author_name": "Prasit Kh",
            "author_id": "pfbid02iHz6rLFmr4A83XUjdMknXdmjSi27jPRFhGg9dxGLMJN7XaVcLp8EmjSLJLcManZSl",
            "threading_depth": 0,
            "post_url": "https://www.facebook.com/prasit.kh.2024",
        }

        assert_that(result).contains_key(
            "id",
            "timestamp",
            "parent_comment_id",
            "content",
            "url",
            "like_count",
            "reply_count",
            "engagement_count",
            "author_name",
            "author_id",
            "threading_depth",
            "post_url",
        )
        assert_that(result["id"]).is_equal_to(
            "Y29tbWVudDoxMTE4NzkyMzAwMDE2NTg5XzExMTg4MDA4ODY2ODIzOTc="
        )
        assert_that(result["timestamp"]).is_equal_to(
            pendulum.datetime(2025, 4, 30, 2, 44, 15, tz="UTC")
        )
        assert_that(result["parent_comment_id"]).is_equal_to(None)
        assert_that(result["content"]).is_equal_to("พักอยู่อ้อมน้อยขอข้อมูลเพิ่มเติม")
        assert_that(result["url"]).is_equal_to(
            "https://www.facebook.com/groups/330668108829016/posts/1118792300016589/?comment_id=1118800886682397"
        )
        assert_that(result["like_count"]).is_equal_to(7)
        assert_that(result["reply_count"]).is_equal_to(3)
        assert_that(result["engagement_count"]).is_equal_to(10)
        assert_that(result["author_name"]).is_equal_to("Prasit Kh")
        assert_that(result["author_id"]).is_equal_to(
            "pfbid02iHz6rLFmr4A83XUjdMknXdmjSi27jPRFhGg9dxGLMJN7XaVcLp8EmjSLJLcManZSl"
        )
        assert_that(result["threading_depth"]).is_equal_to(0)
        assert_that(result["post_url"]).is_equal_to(
            "https://www.facebook.com/groups/330668108829016/posts/1118792300016589/"
        )



class TestInstagramTransformers:
    def test_instagram_post_transformer(self):
        # Arrange
        transformer = InstagramPostOutputTransformer()
        input_data = {
            "id": "3612762149613741495_6068575352",
            "code": "DIjHAiptCW3",
            "url": "https://www.instagram.com/p/DIjHAiptCW3/",
            "createdAt": "2025-04-17T13:00:31.000Z",
            "likeCount": 4,
            "commentCount": 2,
            "caption": 'นสพ.มิติหุ้น "ชี้ชัดทุกการลงทุน" ฉบับวันที่ 18  เมษายน 2568 อ่านต่อ >>> https://www.mitihoon.com/2025/04/17/536816/\n\n#MITIHOON #หุ้น #หุ้นไทย #EA #MAJOR #MEDEZE #VCOM #BANPU #STOCKMASTER#เมดีซ',
            "isLikeAndViewCountsDisabled": False,
            "isPinned": False,
            "isPaidPartnership": False,
            "owner": {
                "id": "6068575352",
                "username": "mitihoon",
                "fullName": "Mitihoon",
                "profilePicUrl": "https://scontent-vie1-1.cdninstagram.com/v/t51.2885-19/25010023_182055199200098_909518814520016896_n.jpg?stp=dst-jpg_e0_s150x150_tt6&_nc_ht=scontent-vie1-1.cdninstagram.com&_nc_cat=102&_nc_oc=Q6cZ2QEGRHEw_MZYGZhOKQ-gxy3aSI1-a2c2_DT8gPIbF1jYnj3JqgVC4xVQuq8rwz379xM&_nc_ohc=z8ewTyHQRVIQ7kNvwEciSKp&_nc_gid=EEsrC52X3qfXik3fPUEHKQ&edm=AMKDjl4BAAAA&ccb=7-5&oh=00_AfEDYGHEzmbFmTdKz3O-vhJED9TvkvKkf8IpRL9rl2fQtQ&oe=681A2B95&_nc_sid=472314",
                "isPrivate": False,
                "isVerified": False,
            },
            "location": None,
            "isCarousel": False,
            "isVideo": False,
            "image": {
                "url": "https://image_url",
                "width": 855,
                "height": 1080,
            },
            "video": {
                "id": "877541914382484v",
                "url": "https://video_url",
                "width": 720,
                "height": 1106,
                "duration": 60.047,
                "playCount": None,
            },
            "audio": None,
        }

        # Act
        result = transformer.transform(input_data)

        # Assert
        expected_output = {
            "id": "DIjHAiptCW3",
            "timestamp": pendulum.datetime(2025, 4, 17, 13, 00, 31, tz="UTC"),
            "content": 'นสพ.มิติหุ้น "ชี้ชัดทุกการลงทุน" ฉบับวันที่ 18  เมษายน 2568 อ่านต่อ >>> https://www.mitihoon.com/2025/04/17/536816/  #MITIHOON #หุ้น #หุ้นไทย #EA #MAJOR #MEDEZE #VCOM #BANPU #STOCKMASTER#เมดีซ',
            "url": "https://www.instagram.com/p/DIjHAiptCW3/",
            "hashtags": [
                "#MITIHOON",
                "#หุ้น",
                "#หุ้นไทย",
                "#EA",
                "#MAJOR",
                "#MEDEZE",
                "#VCOM",
                "#BANPU",
                "#STOCKMASTER",
                "#เมดีซ",
            ],
            "like_count": 4,
            "comment_count": 2,
            "author_name": "mitihoon",
            "author_id": "6068575352",
            "engagement_count": 6,
            "video_url": "https://video_url/",
            "image_url": "https://image_url/",
        }
        assert_that(result).contains_key(
            "id",
            "timestamp",
            "content",
            "url",
            "hashtags",
            "like_count",
            "comment_count",
            "author_name",
            "author_id",
            "engagement_count",
            "video_url",
            "image_url",
        )

        assert_that(result["id"]).is_equal_to("DIjHAiptCW3")
        assert_that(result["timestamp"]).is_equal_to(
            pendulum.datetime(2025, 4, 17, 13, 00, 31, tz="UTC")
        )
        assert_that(result["content"]).is_equal_to(
            'นสพ.มิติหุ้น "ชี้ชัดทุกการลงทุน" ฉบับวันที่ 18  เมษายน 2568 อ่านต่อ >>> https://www.mitihoon.com/2025/04/17/536816/  #MITIHOON #หุ้น #หุ้นไทย #EA #MAJOR #MEDEZE #VCOM #BANPU #STOCKMASTER#เมดีซ'
        )
        assert_that(result["url"]).is_equal_to(
            "https://www.instagram.com/p/DIjHAiptCW3/"
        )
        assert_that(result["hashtags"]).is_equal_to(
            [
                "#MITIHOON",
                "#หุ้น",
                "#หุ้นไทย",
                "#EA",
                "#MAJOR",
                "#MEDEZE",
                "#VCOM",
                "#BANPU",
                "#STOCKMASTER",
                "#เมดีซ",
            ]
        )
        assert_that(result["like_count"]).is_equal_to(4)
        assert_that(result["comment_count"]).is_equal_to(2)
        assert_that(result["author_name"]).is_equal_to("mitihoon")
        assert_that(result["author_id"]).is_equal_to("6068575352")
        assert_that(result["engagement_count"]).is_equal_to(6)
        assert_that(result["video_url"]).is_equal_to("https://video_url/")
        assert_that(result["image_url"]).is_equal_to("https://image_url/")
        assert_that(result).is_equal_to(expected_output)

    def test_instagram_comment_transformer(self):
        # Arrange
        transformer = InstagramCommentOutputTransformer()
        input_data = {
            "postId": "DHFOlERz4CC",
            "id": "18336584827080104",
            "userId": 8795648,
            "message": "https://youtu.be/4C6ZqnW6k60",
            "createdAt": **********,
            "shareEnabled": False,
            "user": {
                "id": 8795648,
                "username": "pinpisit",
                "image": "https://scontent-cph2-1.cdninstagram.com/v/t51.2885-19/484470822_973371068340871_1126205802192642319_n.jpg?stp=dst-jpg_e0_s150x150_tt6&_nc_ht=scontent-cph2-1.cdninstagram.com&_nc_cat=107&_nc_oc=Q6cZ2QHkZ26wJaL0wx_SCIj6t1hIXf4qHHrDs3gg_GzzRFF33084X8Xc2On8jh-ClzlUokA&_nc_ohc=cEAJMv1zzZ8Q7kNvwHy5d1T&_nc_gid=gwR7NOTSgskmwoC9VLvcDg&edm=AId3EpQBAAAA&ccb=7-5&oh=00_AfNtkzuWv0kTuDChxRJ4aJ07jyXXItLU__85oA-BSMPWIA&oe=6846F1A1&_nc_sid=f5838a",
                "isVerified": False,
            },
            "isRanked": False,
            "likeCount": 1,
            "search_term": "https://www.instagram.com/p/DHFOlERz4CC/,https://www.instagram.com/p/DHLefgXJy1a/",
        }
        # Act
        result = transformer.transform(input_data)
        # Assert
        expected_output = {
            "id": "18336584827080104",
            "timestamp": pendulum.datetime(2025, 3, 12, 2, 6, 50, tz="UTC"),
            "content": "https://youtu.be/4C6ZqnW6k60",
            "like_count": 1,
            "engagement_count": 1,
            "author_name": "pinpisit",
            "author_id": 8795648,
            "post_id": "DHFOlERz4CC",
        }
        assert_that(result).contains_key(
            "id",
            "timestamp",
            "content",
            "like_count",
            "engagement_count",
            "author_name",
            "author_id",
            "post_id",
        )
        assert_that(result["id"]).is_equal_to("18336584827080104")
        assert_that(result["timestamp"]).is_equal_to(
            pendulum.datetime(2025, 3, 12, 2, 6, 50, tz="UTC")
        )
        assert_that(result["content"]).is_equal_to("https://youtu.be/4C6ZqnW6k60")
        assert_that(result["like_count"]).is_equal_to(1)
        assert_that(result["engagement_count"]).is_equal_to(1)
        assert_that(result["author_name"]).is_equal_to("pinpisit")
        assert_that(result["author_id"]).is_equal_to(8795648)
        assert_that(result["post_id"]).is_equal_to("DHFOlERz4CC")
        assert_that(result).is_equal_to(expected_output)


class TestYoutubeTransformers:
    def test_youtube_post_transformer(self):
        # Arrange
        transformer = YoutubePostOutputTransformer()
        input_data = {
        "title": "เทคโนโลยีการแพทย์ไทยไปไกลกว่าที่คิด จากสเต็มเซลล์สู่ความหวังของหุ้นนวัตกรรมไทย",
        "type": "video",
        "id": "0D3rwlYXttg",
        "url": "https://www.youtube.com/watch?v=0D3rwlYXttg",
        "thumbnailUrl": "https://i.ytimg.com/vi/0D3rwlYXttg/maxresdefault.jpg",
        "viewCount": 4234,
        "date": "2025-04-27T04:00:01.000Z",
        "likes": 152,
        "location": None,
        "channelName": "Business Tomorrow",
        "channelUrl": "https://www.youtube.com/@BusinessTomorrowTH",
        "channelId": "UCyQ6wsBD5-G7yTdf4Kwlplw",
        "channelUsername": "BusinessTomorrowTH",
        "numberOfSubscribers": 32500,
        "duration": "00:55:41",
        "commentsCount": 58,
        "text": "ตลาดหุ้นไทยอาจดูนิ่ง ๆ แต่ใช่ว่าจะไม่มี “หุ้นเปลี่ยนโลก”\nวันนี้เราจะพาคุณไปรู้จัก MEDEZE บริษัทไทยที่เริ่มจากธุรกิจเก็บสเต็มเซลล์…\nและกำลังผสานเทคโนโลยี AI เพื่อพัฒนาแพลตฟอร์มการแพทย์ระดับสากล\n\nMEDEZE มีอะไรที่ทำให้เหนือกว่าคู่แข่ง ? และ อนาคตของหุ้นนวัตกรรมนี้จะไปไกลแค่ไหน ? \n\nเจาะลึกกับ  นพ.วีรพล เขมะรังสรรค์ ผู้ร่วมก่อตั้งและ  ประธานเจ้าหน้าที่บริหาร บริษัท เมดีซ กรุ๊ป จำกัด (มหาชน) หรือ MEDEZE ธนาคารเก็บสเต็มเซลล์แห่งแรกในประเทศไทย \n\nดำเนินรายการโดย กีตาร์ - วรินท์มาศ ปัญญาดี\nChief Executive Editor - Business Tomorrow\n\n------------------------------------\n\n00:00 - Highlight\n01:50 - ทักทาย\n02:08 - ภาพธุรกิจ Stem Cells ในปีนี้ของระดับโลกและในประเทศไทย\n07:38 - ทิศทาง เป้าหมายการเติบโตในอุตสาหกรรม?คนไทยมีโอกาสในการเข้าถึงผลิตภัณฑ์?\n14:27 - ผลประกอบการ Medeze เติบโตขึ้นเกินเท่าตัว ปัจจัยอะไรที่ทำให้เติบโต? \n16:00 - ปัจจัยที่หนุนให้กำไรเติบโต? อัตราการเกิดใหม่ของเด็กลดลงจะกระทบต่อธุรกิจ?\n21:34 - Net profit margin ของ Medeze เติบโตอย่างต่อเนื่อง ที่มาของการเติบโตคืออะไร\n28:50 - แผนการธุรกิจในตลาดต่างประเทศ\n36:58 - อะไรที่เป็นเกราะป้องกันคู่แข่งในตลาด หรือ บริษัทอื่นๆที่ให้บริการทางการแพทย์ ที่อาจเข้ามาเป็นผู้เล่นรายใหม่ในตลาด?\n40:46 - การลงทุนด้าน AI (Medeze plus auto matching software) จะช่วยสร้างโอกาสการเติบโตอย่างไรในอนาคต?\n44:47 - Product ใหม่ตัวต่อไปชอง Medeze จะเกี่ยวกับอะไร? และจะเติบโตอย่างไร\n52:08 - วิสัยทัศน์ของ Medeze คืออะไร และแผนในระยะยาววางสเตตัสของบริษัทไว้อย่างไร\n\nในรายการ Business Tomorrow ON GROUND by TraderKP\n\n------------------------------------\n\nติดตามทุกข่าวสาร + สาระการลงทุนที่ “ทันโลก” กับทีม Trader KP ได้ทุกช่องทางที่นี้ 😊 https://linktr.ee/traderkp\n\n-----------------------\n\n📌 สมัคร Youtube Membership เพื่อรับสิทธิประโยชน์ต่างๆ\n   / @traderkp  \n\n-----------------------\n\nBusiness Tomorrow และ ทันโลกกับ Trader KP เป็นช่องที่นำเสนอข่าวสารและความรู้เกี่ยวกับการลงทุน ตลาดการเงินและธุรกิจให้แก่คนไทยผ่านสื่อสังคมออนไลน์และช่องทางออนไลน์ และ รับผลิตสื่อ สัมมนาและงานสร้างสรรค์ด้านการเงินการลงทุนทุกประเภท โดยพันธกิจของบริษัทคือการยกระดับความรู้ด้านการเงินและเป็นแหล่งข่าวธุรกิจ เศรษฐกิจและการเงินสำหรับคนไทย ปัจจุบันมีผู้ติดตามรวมกันกว่า 2 ล้านรายทั่วทุกแพลตฟอร์ม\n\nติดต่องาน : <EMAIL>\n\n-----------------------\n\n🔈 กดติดตาม Subscribe ช่อง YouTube ด้านการลงทุนที่ครบถ้วนได้ที่ \"Trader KP\" และ \"Business Tomorrow\"\nYoutube: https://bit.ly/3oz15kq\nYoutube:    / @businesstomorrowth  \n\nติดตาม TraderKP และ Business Tomorrow ช่องทางอื่นๆ ได้ที่\nFacebook :   / businesstomorrowth  \nFacebook :   / oiltraderkp  \nTwitter :   / traderkp_th  \nBlockdit : https://www.blockdit.com/oiltradingkp\nTiktok :   / traderkp  \nIG :   / traderkp.ig  \n\n--------------------------------\n\n** คลิปนี้เป็นความเห็นส่วนบุคคล มิได้เป็นการชี้แนะใดๆ กรุณารับชมด้วยวิจารณญาณ **\n\n#stemcells #biotechnology #Medeze #หุ้นไทย #ตลาดหุ้น #การลงทุน #หุ้น #หุ้นไทย #นวัตกรรม #เทคโนโลยี #การลงทุน #ตลาดหุ้น #บทสัมภาษณ์พิเศษ #โอกาสการเติบโต #ธุรกิจไทย #อนาคต",
        "descriptionLinks": [
            {
                "url": "https://www.youtube.com/watch?v=0D3rwlYXttg",
                "text": "00:00"
            },
            {
                "url": "https://www.youtube.com/watch?v=0D3rwlYXttg&t=110s",
                "text": "01:50"
            },
            {
                "url": "https://www.youtube.com/watch?v=0D3rwlYXttg&t=128s",
                "text": "02:08"
            },
            {
                "url": "https://www.youtube.com/watch?v=0D3rwlYXttg&t=458s",
                "text": "07:38"
            },
            {
                "url": "https://www.youtube.com/watch?v=0D3rwlYXttg&t=867s",
                "text": "14:27"
            },
            {
                "url": "https://www.youtube.com/watch?v=0D3rwlYXttg&t=960s",
                "text": "16:00"
            },
            {
                "url": "https://www.youtube.com/watch?v=0D3rwlYXttg&t=1294s",
                "text": "21:34"
            },
            {
                "url": "https://www.youtube.com/watch?v=0D3rwlYXttg&t=1730s",
                "text": "28:50"
            },
            {
                "url": "https://www.youtube.com/watch?v=0D3rwlYXttg&t=2218s",
                "text": "36:58"
            },
            {
                "url": "https://www.youtube.com/watch?v=0D3rwlYXttg&t=2446s",
                "text": "40:46"
            },
            {
                "url": "https://www.youtube.com/watch?v=0D3rwlYXttg&t=2687s",
                "text": "44:47"
            },
            {
                "url": "https://www.youtube.com/watch?v=0D3rwlYXttg&t=3128s",
                "text": "52:08"
            },
            {
                "url": "https://linktr.ee/traderkp",
                "text": "https://linktr.ee/traderkp"
            },
            {
                "url": "https://www.youtube.com/channel/UC1fuv1VUJennQwwpDGaAiHQ/join",
                "text": "   / @traderkp  "
            },
            {
                "url": "https://bit.ly/3oz15kq",
                "text": "https://bit.ly/3oz15kq"
            },
            {
                "url": "https://www.youtube.com/channel/UCyQ6wsBD5-G7yTdf4Kwlplw",
                "text": "   / @businesstomorrowth  "
            },
            {
                "url": "https://www.facebook.com/BusinessTomorrowTH",
                "text": "  / businesstomorrowth  "
            },
            {
                "url": "https://www.facebook.com/OilTraderKP",
                "text": "  / oiltraderkp  "
            },
            {
                "url": "https://twitter.com/TraderKP_TH",
                "text": "  / traderkp_th  "
            },
            {
                "url": "https://www.blockdit.com/oiltradingkp",
                "text": "https://www.blockdit.com/oiltradingkp"
            },
            {
                "url": "https://www.tiktok.com/@traderkp",
                "text": "  / traderkp  "
            },
            {
                "url": "https://www.instagram.com/traderkp.ig/",
                "text": "  / traderkp.ig  "
            },
            {
                "url": "https://www.youtube.com/hashtag/stemcells",
                "text": "#stemcells"
            },
            {
                "url": "https://www.youtube.com/hashtag/biotechnology",
                "text": "#biotechnology"
            },
            {
                "url": "https://www.youtube.com/hashtag/medeze",
                "text": "#Medeze"
            },
            {
                "url": "https://www.youtube.com/hashtag/%E0%B8%AB%E0%B8%B8%E0%B9%89%E0%B8%99%E0%B9%84%E0%B8%97%E0%B8%A2",
                "text": "#หุ้นไทย"
            },
            {
                "url": "https://www.youtube.com/hashtag/%E0%B8%95%E0%B8%A5%E0%B8%B2%E0%B8%94%E0%B8%AB%E0%B8%B8%E0%B9%89%E0%B8%99",
                "text": "#ตลาดหุ้น"
            },
            {
                "url": "https://www.youtube.com/hashtag/%E0%B8%81%E0%B8%B2%E0%B8%A3%E0%B8%A5%E0%B8%87%E0%B8%97%E0%B8%B8%E0%B8%99",
                "text": "#การลงทุน"
            },
            {
                "url": "https://www.youtube.com/hashtag/%E0%B8%AB%E0%B8%B8%E0%B9%89%E0%B8%99",
                "text": "#หุ้น"
            },
            {
                "url": "https://www.youtube.com/hashtag/%E0%B8%AB%E0%B8%B8%E0%B9%89%E0%B8%99%E0%B9%84%E0%B8%97%E0%B8%A2",
                "text": "#หุ้นไทย"
            },
            {
                "url": "https://www.youtube.com/hashtag/%E0%B8%99%E0%B8%A7%E0%B8%B1%E0%B8%95%E0%B8%81%E0%B8%A3%E0%B8%A3%E0%B8%A1",
                "text": "#นวัตกรรม"
            },
            {
                "url": "https://www.youtube.com/hashtag/%E0%B9%80%E0%B8%97%E0%B8%84%E0%B9%82%E0%B8%99%E0%B9%82%E0%B8%A5%E0%B8%A2%E0%B8%B5",
                "text": "#เทคโนโลยี"
            },
            {
                "url": "https://www.youtube.com/hashtag/%E0%B8%81%E0%B8%B2%E0%B8%A3%E0%B8%A5%E0%B8%87%E0%B8%97%E0%B8%B8%E0%B8%99",
                "text": "#การลงทุน"
            },
            {
                "url": "https://www.youtube.com/hashtag/%E0%B8%95%E0%B8%A5%E0%B8%B2%E0%B8%94%E0%B8%AB%E0%B8%B8%E0%B9%89%E0%B8%99",
                "text": "#ตลาดหุ้น"
            },
            {
                "url": "https://www.youtube.com/hashtag/%E0%B8%9A%E0%B8%97%E0%B8%AA%E0%B8%B1%E0%B8%A1%E0%B8%A0%E0%B8%B2%E0%B8%A9%E0%B8%93%E0%B9%8C%E0%B8%9E%E0%B8%B4%E0%B9%80%E0%B8%A8%E0%B8%A9",
                "text": "#บทสัมภาษณ์พิเศษ"
            },
            {
                "url": "https://www.youtube.com/hashtag/%E0%B9%82%E0%B8%AD%E0%B8%81%E0%B8%B2%E0%B8%AA%E0%B8%81%E0%B8%B2%E0%B8%A3%E0%B9%80%E0%B8%95%E0%B8%B4%E0%B8%9A%E0%B9%82%E0%B8%95",
                "text": "#โอกาสการเติบโต"
            },
            {
                "url": "https://www.youtube.com/hashtag/%E0%B8%98%E0%B8%B8%E0%B8%A3%E0%B8%81%E0%B8%B4%E0%B8%88%E0%B9%84%E0%B8%97%E0%B8%A2",
                "text": "#ธุรกิจไทย"
            },
            {
                "url": "https://www.youtube.com/hashtag/%E0%B8%AD%E0%B8%99%E0%B8%B2%E0%B8%84%E0%B8%95",
                "text": "#อนาคต"
            }
        ],
        "subtitles": None,
        "order": 0,
        "commentsTurnedOff": False,
        "comments": None,
        "fromYTUrl": "https://www.youtube.com/results?search_query=%22%E0%B9%80%E0%B8%A1%E0%B8%94%E0%B8%B5%E0%B8%8B%22&sp=CAISAggD",
        "isMonetized": None,
        "hashtags": [
            "#stemcells",
            "#หุ้น",
            "#หุ้นไทย"
        ],
        "formats": [],
        "isMembersOnly": False,
        "input": "\"เมดีซ\""
    }

        # Act
        result = transformer.transform(input_data)

        # Assert
        expected_output = {
            "id": "0D3rwlYXttg",
            "timestamp": pendulum.datetime(2025, 4, 27, 4, 0, 1, tz="UTC"),
            "url": "https://www.youtube.com/watch?v=0D3rwlYXttg",
            "title": "เทคโนโลยีการแพทย์ไทยไปไกลกว่าที่คิด จากสเต็มเซลล์สู่ความหวังของหุ้นนวัตกรรมไทย",
            "content": """ตลาดหุ้นไทยอาจดูนิ่ง ๆ แต่ใช่ว่าจะไม่มี “หุ้นเปลี่ยนโลก” วันนี้เราจะพาคุณไปรู้จัก MEDEZE บริษัทไทยที่เริ่มจากธุรกิจเก็บสเต็มเซลล์… และกำลังผสานเทคโนโลยี AI เพื่อพัฒนาแพลตฟอร์มการแพทย์ระดับสากล  MEDEZE มีอะไรที่ทำให้เหนือกว่าคู่แข่ง ? และ อนาคตของหุ้นนวัตกรรมนี้จะไปไกลแค่ไหน ?   เจาะลึกกับ  นพ.วีรพล เขมะรังสรรค์ ผู้ร่วมก่อตั้งและ  ประธานเจ้าหน้าที่บริหาร บริษัท เมดีซ กรุ๊ป จำกัด (มหาชน) หรือ MEDEZE ธนาคารเก็บสเต็มเซลล์แห่งแรกในประเทศไทย   ดำเนินรายการโดย กีตาร์ - วรินท์มาศ ปัญญาดี Chief Executive Editor - Business Tomorrow  ------------------------------------  00:00 - Highlight 01:50 - ทักทาย 02:08 - ภาพธุรกิจ Stem Cells ในปีนี้ของระดับโลกและในประเทศไทย 07:38 - ทิศทาง เป้าหมายการเติบโตในอุตสาหกรรม?คนไทยมีโอกาสในการเข้าถึงผลิตภัณฑ์? 14:27 - ผลประกอบการ Medeze เติบโตขึ้นเกินเท่าตัว ปัจจัยอะไรที่ทำให้เติบโต?  16:00 - ปัจจัยที่หนุนให้กำไรเติบโต? อัตราการเกิดใหม่ของเด็กลดลงจะกระทบต่อธุรกิจ? 21:34 - Net profit margin ของ Medeze เติบโตอย่างต่อเนื่อง ที่มาของการเติบโตคืออะไร 28:50 - แผนการธุรกิจในตลาดต่างประเทศ 36:58 - อะไรที่เป็นเกราะป้องกันคู่แข่งในตลาด หรือ บริษัทอื่นๆที่ให้บริการทางการแพทย์ ที่อาจเข้ามาเป็นผู้เล่นรายใหม่ในตลาด? 40:46 - การลงทุนด้าน AI (Medeze plus auto matching software) จะช่วยสร้างโอกาสการเติบโตอย่างไรในอนาคต? 44:47 - Product ใหม่ตัวต่อไปชอง Medeze จะเกี่ยวกับอะไร? และจะเติบโตอย่างไร 52:08 - วิสัยทัศน์ของ Medeze คืออะไร และแผนในระยะยาววางสเตตัสของบริษัทไว้อย่างไร  ในรายการ Business Tomorrow ON GROUND by TraderKP  ------------------------------------  ติดตามทุกข่าวสาร + สาระการลงทุนที่ “ทันโลก” กับทีม Trader KP ได้ทุกช่องทางที่นี้ 😊 https://linktr.ee/traderkp  -----------------------  📌 สมัคร Youtube Membership เพื่อรับสิทธิประโยชน์ต่างๆ    / @traderkp    -----------------------  Business Tomorrow และ ทันโลกกับ Trader KP เป็นช่องที่นำเสนอข่าวสารและความรู้เกี่ยวกับการลงทุน ตลาดการเงินและธุรกิจให้แก่คนไทยผ่านสื่อสังคมออนไลน์และช่องทางออนไลน์ และ รับผลิตสื่อ สัมมนาและงานสร้างสรรค์ด้านการเงินการลงทุนทุกประเภท โดยพันธกิจของบริษัทคือการยกระดับความรู้ด้านการเงินและเป็นแหล่งข่าวธุรกิจ เศรษฐกิจและการเงินสำหรับคนไทย ปัจจุบันมีผู้ติดตามรวมกันกว่า 2 ล้านรายทั่วทุกแพลตฟอร์ม  ติดต่องาน : <EMAIL>  -----------------------  🔈 กดติดตาม Subscribe ช่อง YouTube ด้านการลงทุนที่ครบถ้วนได้ที่ "Trader KP" และ "Business Tomorrow" Youtube: https://bit.ly/3oz15kq Youtube:    / @businesstomorrowth    ติดตาม TraderKP และ Business Tomorrow ช่องทางอื่นๆ ได้ที่ Facebook :   / businesstomorrowth   Facebook :   / oiltraderkp   Twitter :   / traderkp_th   Blockdit : https://www.blockdit.com/oiltradingkp Tiktok :   / traderkp   IG :   / traderkp.ig    --------------------------------  ** คลิปนี้เป็นความเห็นส่วนบุคคล มิได้เป็นการชี้แนะใดๆ กรุณารับชมด้วยวิจารณญาณ **  #stemcells #biotechnology #Medeze #หุ้นไทย #ตลาดหุ้น #การลงทุน #หุ้น #หุ้นไทย #นวัตกรรม #เทคโนโลยี #การลงทุน #ตลาดหุ้น #บทสัมภาษณ์พิเศษ #โอกาสการเติบโต #ธุรกิจไทย #อนาคต""",
            "hashtags": ["#stemcells", "#หุ้น", "#หุ้นไทย"],
            "like_count": 152,
            "comment_count": 58,
            "view_count": 4234,
            "video_type": "video",
            "author_name": "Business Tomorrow",
            "author_id": "UCyQ6wsBD5-G7yTdf4Kwlplw",
            "author_username": "BusinessTomorrowTH",
            "engagement_count": 210,
        }
    
        assert_that(result).contains_key(
            "id",
            "timestamp",
            "url",
            "title",
            "content",
            "hashtags",
            "like_count",
            "comment_count",
            "view_count",
            "video_type",
            "author_name",
            "author_id",
            "author_username",
            "engagement_count",
        )
        assert_that(result["id"]).is_equal_to("0D3rwlYXttg")
        assert_that(result["timestamp"]).is_equal_to(
            pendulum.datetime(2025, 4, 27, 4, 0, 1, tz="UTC")
            )
        assert_that(result["url"]).is_equal_to("https://www.youtube.com/watch?v=0D3rwlYXttg")
        assert_that(result["title"]).is_equal_to("เทคโนโลยีการแพทย์ไทยไปไกลกว่าที่คิด จากสเต็มเซลล์สู่ความหวังของหุ้นนวัตกรรมไทย")
        assert_that(result["content"]).is_equal_to("""ตลาดหุ้นไทยอาจดูนิ่ง ๆ แต่ใช่ว่าจะไม่มี “หุ้นเปลี่ยนโลก” วันนี้เราจะพาคุณไปรู้จัก MEDEZE บริษัทไทยที่เริ่มจากธุรกิจเก็บสเต็มเซลล์… และกำลังผสานเทคโนโลยี AI เพื่อพัฒนาแพลตฟอร์มการแพทย์ระดับสากล  MEDEZE มีอะไรที่ทำให้เหนือกว่าคู่แข่ง ? และ อนาคตของหุ้นนวัตกรรมนี้จะไปไกลแค่ไหน ?   เจาะลึกกับ  นพ.วีรพล เขมะรังสรรค์ ผู้ร่วมก่อตั้งและ  ประธานเจ้าหน้าที่บริหาร บริษัท เมดีซ กรุ๊ป จำกัด (มหาชน) หรือ MEDEZE ธนาคารเก็บสเต็มเซลล์แห่งแรกในประเทศไทย   ดำเนินรายการโดย กีตาร์ - วรินท์มาศ ปัญญาดี Chief Executive Editor - Business Tomorrow  ------------------------------------  00:00 - Highlight 01:50 - ทักทาย 02:08 - ภาพธุรกิจ Stem Cells ในปีนี้ของระดับโลกและในประเทศไทย 07:38 - ทิศทาง เป้าหมายการเติบโตในอุตสาหกรรม?คนไทยมีโอกาสในการเข้าถึงผลิตภัณฑ์? 14:27 - ผลประกอบการ Medeze เติบโตขึ้นเกินเท่าตัว ปัจจัยอะไรที่ทำให้เติบโต?  16:00 - ปัจจัยที่หนุนให้กำไรเติบโต? อัตราการเกิดใหม่ของเด็กลดลงจะกระทบต่อธุรกิจ? 21:34 - Net profit margin ของ Medeze เติบโตอย่างต่อเนื่อง ที่มาของการเติบโตคืออะไร 28:50 - แผนการธุรกิจในตลาดต่างประเทศ 36:58 - อะไรที่เป็นเกราะป้องกันคู่แข่งในตลาด หรือ บริษัทอื่นๆที่ให้บริการทางการแพทย์ ที่อาจเข้ามาเป็นผู้เล่นรายใหม่ในตลาด? 40:46 - การลงทุนด้าน AI (Medeze plus auto matching software) จะช่วยสร้างโอกาสการเติบโตอย่างไรในอนาคต? 44:47 - Product ใหม่ตัวต่อไปชอง Medeze จะเกี่ยวกับอะไร? และจะเติบโตอย่างไร 52:08 - วิสัยทัศน์ของ Medeze คืออะไร และแผนในระยะยาววางสเตตัสของบริษัทไว้อย่างไร  ในรายการ Business Tomorrow ON GROUND by TraderKP  ------------------------------------  ติดตามทุกข่าวสาร + สาระการลงทุนที่ “ทันโลก” กับทีม Trader KP ได้ทุกช่องทางที่นี้ 😊 https://linktr.ee/traderkp  -----------------------  📌 สมัคร Youtube Membership เพื่อรับสิทธิประโยชน์ต่างๆ    / @traderkp    -----------------------  Business Tomorrow และ ทันโลกกับ Trader KP เป็นช่องที่นำเสนอข่าวสารและความรู้เกี่ยวกับการลงทุน ตลาดการเงินและธุรกิจให้แก่คนไทยผ่านสื่อสังคมออนไลน์และช่องทางออนไลน์ และ รับผลิตสื่อ สัมมนาและงานสร้างสรรค์ด้านการเงินการลงทุนทุกประเภท โดยพันธกิจของบริษัทคือการยกระดับความรู้ด้านการเงินและเป็นแหล่งข่าวธุรกิจ เศรษฐกิจและการเงินสำหรับคนไทย ปัจจุบันมีผู้ติดตามรวมกันกว่า 2 ล้านรายทั่วทุกแพลตฟอร์ม  ติดต่องาน : <EMAIL>  -----------------------  🔈 กดติดตาม Subscribe ช่อง YouTube ด้านการลงทุนที่ครบถ้วนได้ที่ "Trader KP" และ "Business Tomorrow" Youtube: https://bit.ly/3oz15kq Youtube:    / @businesstomorrowth    ติดตาม TraderKP และ Business Tomorrow ช่องทางอื่นๆ ได้ที่ Facebook :   / businesstomorrowth   Facebook :   / oiltraderkp   Twitter :   / traderkp_th   Blockdit : https://www.blockdit.com/oiltradingkp Tiktok :   / traderkp   IG :   / traderkp.ig    --------------------------------  ** คลิปนี้เป็นความเห็นส่วนบุคคล มิได้เป็นการชี้แนะใดๆ กรุณารับชมด้วยวิจารณญาณ **  #stemcells #biotechnology #Medeze #หุ้นไทย #ตลาดหุ้น #การลงทุน #หุ้น #หุ้นไทย #นวัตกรรม #เทคโนโลยี #การลงทุน #ตลาดหุ้น #บทสัมภาษณ์พิเศษ #โอกาสการเติบโต #ธุรกิจไทย #อนาคต""")
        assert_that(result["hashtags"]).is_equal_to(["#stemcells", "#หุ้น", "#หุ้นไทย"])
        assert_that(result["like_count"]).is_equal_to(152)
        assert_that(result["comment_count"]).is_equal_to(58)
        assert_that(result["view_count"]).is_equal_to(4234)
        assert_that(result["video_type"]).is_equal_to("video")
        assert_that(result["author_name"]).is_equal_to("Business Tomorrow")
        assert_that(result["author_id"]).is_equal_to("UCyQ6wsBD5-G7yTdf4Kwlplw")
        assert_that(result["author_username"]).is_equal_to("BusinessTomorrowTH")
        assert_that(result["engagement_count"]).is_equal_to(210)
        assert_that(result).is_equal_to(expected_output)



    def test_youtube_comment_transformer(self):
        # Can't test exact timestamp due to "days ago" format, but can check it's a datetime
        transformer = YoutubeCommentOutputTransformer()

        input_data =     {
        "cid": "UgwAm8-qLQHlbYbLA-t4AaABAg",
        "replyToCid": None,
        "type": "comment",
        "publishedTimeText": "2 days ago",
        "comment": "น่าจะเป็นเรื่องของคนรำ่รวยรักษาสุขภาพครั้งละเป็นแสนแต่…ความไม่แน่นอนมันก็ยังคงอยู่แต่เงินเราหายไปแบบตามไม่ได้เพราะมันเข้าไปในตัวเราแล้ว😂ให้ซาอุมารักษาดีที่สุดเพราะเงินเค้าเยอะอ่ะ",
        "author": "@suthidaprompongsri3289",
        "authorIsChannelOwner": False,
        "replyCount": 7,
        "voteCount": 40,
        "hasCreatorHeart": False,
        "videoId": "0D3rwlYXttg",
        "pageUrl": "https://www.youtube.com/watch?v=0D3rwlYXttg",
        "commentsCount": 58,
        "title": "เทคโนโลยีการแพทย์ไทยไปไกลกว่าที่คิด จากสเต็มเซลล์สู่ความหวังของหุ้นนวัตกรรมไทย"
        }

        # Act
        result = transformer.transform(input_data)

        # Assert
        expected_output = {
            "id": "UgwAm8-qLQHlbYbLA-t4AaABAg",
            "timestamp": pendulum.datetime(2025, 6, 5, 7, 57, 32, 703075, tz="UTC"), # 'timestamp': DateTime(2025, 6, 5, 7, 57, 32, 703075, tzinfo=Timezone('UTC'))
            "type": "comment",
            "parent_comment_id": None,
            "content": "น่าจะเป็นเรื่องของคนรำ่รวยรักษาสุขภาพครั้งละเป็นแสนแต่…ความไม่แน่นอนมันก็ยังคงอยู่แต่เงินเราหายไปแบบตามไม่ได้เพราะมันเข้าไปในตัวเราแล้ว😂ให้ซาอุมารักษาดีที่สุดเพราะเงินเค้าเยอะอ่ะ",
            "like_count": 40,
            "reply_count": 7,
            "engagement_count": 47,
            "author_name": "@suthidaprompongsri3289",
        }
        assert_that(result).contains_key(
            "id",
            "timestamp",
            "type",
            "parent_comment_id",
            "content",
            "like_count",
            "reply_count",
            "engagement_count",
            "author_name",
        )
        assert_that(result["id"]).is_equal_to("UgwAm8-qLQHlbYbLA-t4AaABAg")
        assert_that(result["timestamp"]).is_instance_of(pendulum.DateTime)
        assert_that(result["type"]).is_equal_to("comment")
        assert_that(result["parent_comment_id"]).is_equal_to(None)
        assert_that(result["content"]).is_equal_to("น่าจะเป็นเรื่องของคนรำ่รวยรักษาสุขภาพครั้งละเป็นแสนแต่…ความไม่แน่นอนมันก็ยังคงอยู่แต่เงินเราหายไปแบบตามไม่ได้เพราะมันเข้าไปในตัวเราแล้ว😂ให้ซาอุมารักษาดีที่สุดเพราะเงินเค้าเยอะอ่ะ")
        assert_that(result["like_count"]).is_equal_to(40)
        assert_that(result["reply_count"]).is_equal_to(7)
        assert_that(result["engagement_count"]).is_equal_to(47)
        assert_that(result["author_name"]).is_equal_to("@suthidaprompongsri3289")
        
        # Exclude "timestamp" from both dicts before comparison
        filtered_result = {k: v for k, v in result.items() if k != "timestamp"}
        filtered_expected = {k: v for k, v in expected_output.items() if k != "timestamp"}

        assert_that(filtered_result).is_equal_to(filtered_expected)




class TestTwitterTransformer:
    def test_twitter_transformer(self):
        # Arrange
        transformer = TwitterOutputTransformer()
        input_data = {
        "type": "tweet",
        "id": "1917461328773669152",
        "url": "https://x.com/SuccessChTv/status/1917461328773669152",
        "twitterUrl": "https://twitter.com/SuccessChTv/status/1917461328773669152",
        "text": "MEDEZE ให้การต้อนรับกลุ่มลูกค้า IFCG ให้ข้อมูลด้าน NK Cell เพื่อส่งเสริมองค์ความรู้ทางการแพทย์เชิงลึก \n\nhttps://t.co/msEyj8s12P \n\n#MEDEZE\n#IFCG\n#NKCell https://t.co/ptcq7GZY0l",
        "fullText": "MEDEZE ให้การต้อนรับกลุ่มลูกค้า IFCG ให้ข้อมูลด้าน NK Cell เพื่อส่งเสริมองค์ความรู้ทางการแพทย์เชิงลึก \n\nhttps://t.co/msEyj8s12P \n\n#MEDEZE\n#IFCG\n#NKCell https://t.co/ptcq7GZY0l",
        "source": "Twitter Web App",
        "retweetCount": 2,
        "replyCount": 14,
        "likeCount": 15,
        "quoteCount": 7,
        "viewCount": 17,
        "createdAt": "Wed Apr 30 06:09:40 +0000 2025",
        "lang": "th",
        "bookmarkCount": 3,
        "isReply": False,
        "conversationId": "1917461328773669152",
        "possiblySensitive": False,
        "isPinned": False,
        "author": {
            "type": "user",
            "userName": "SuccessChTv",
            "url": "https://x.com/SuccessChTv",
            "twitterUrl": "https://twitter.com/SuccessChTv",
            "id": "1326006836017815560",
            "name": "Success Channel",
            "isVerified": False,
            "isBlueVerified": False,
            "profilePicture": "https://pbs.twimg.com/profile_images/1680086115066445825/IpJh13_0_normal.jpg",
            "coverPicture": "https://pbs.twimg.com/profile_banners/1326006836017815560/1644772652",
            "description": "รายการเศรษฐกิจ นำเสนอทั้งข่าวเศรษฐกิจ และ Content ที่น่าสนใจ\nฝากข่าว <EMAIL>\nลงโฆษณา 064-225-5646 / 095-497-9392",
            "location": "",
            "followers": 1080,
            "following": 275,
            "status": "",
            "canDm": False,
            "canMediaTag": True,
            "createdAt": "Tue Nov 10 03:41:33 +0000 2020",
            "entities": {
                "description": {
                    "urls": []
                },
                "url": {
                    "urls": [
                        {
                            "display_url": "successchannel.co",
                            "expanded_url": "https://successchannel.co/",
                            "url": "https://t.co/tp44ixcAUB",
                            "indices": [
                                0,
                                23
                            ]
                        }
                    ]
                }
            },
            "fastFollowersCount": 0,
            "favouritesCount": 6678,
            "hasCustomTimelines": False,
            "isTranslator": False,
            "mediaCount": 36291,
            "statusesCount": 36429,
            "withheldInCountries": [],
            "affiliatesHighlightedLabel": {},
            "possiblySensitive": False,
            "pinnedTweetIds": [
                "1585581679623618560"
            ]
        },
        "extendedEntities": {
            "media": [
                {
                    "display_url": "pic.x.com/ptcq7GZY0l",
                    "expanded_url": "https://x.com/SuccessChTv/status/1917461328773669152/photo/1",
                    "id_str": "1917461313573531648",
                    "indices": [
                        152,
                        175
                    ],
                    "media_key": "3_1917461313573531648",
                    "media_url_https": "https://pbs.twimg.com/media/Gpww59IasAA69G8.jpg",
                    "type": "photo",
                    "url": "https://t.co/ptcq7GZY0l",
                    "ext_media_availability": {
                        "status": "Available"
                    },
                    "features": {
                        "large": {
                            "faces": [
                                {
                                    "x": 87,
                                    "y": 159,
                                    "h": 71,
                                    "w": 71
                                },
                                {
                                    "x": 1077,
                                    "y": 611,
                                    "h": 67,
                                    "w": 67
                                },
                                {
                                    "x": 903,
                                    "y": 721,
                                    "h": 97,
                                    "w": 97
                                }
                            ]
                        },
                        "medium": {
                            "faces": [
                                {
                                    "x": 51,
                                    "y": 93,
                                    "h": 42,
                                    "w": 42
                                },
                                {
                                    "x": 631,
                                    "y": 358,
                                    "h": 39,
                                    "w": 39
                                },
                                {
                                    "x": 529,
                                    "y": 422,
                                    "h": 57,
                                    "w": 57
                                }
                            ]
                        },
                        "small": {
                            "faces": [
                                {
                                    "x": 29,
                                    "y": 53,
                                    "h": 23,
                                    "w": 23
                                },
                                {
                                    "x": 357,
                                    "y": 203,
                                    "h": 22,
                                    "w": 22
                                },
                                {
                                    "x": 300,
                                    "y": 239,
                                    "h": 32,
                                    "w": 32
                                }
                            ]
                        },
                        "orig": {
                            "faces": [
                                {
                                    "x": 144,
                                    "y": 262,
                                    "h": 118,
                                    "w": 118
                                },
                                {
                                    "x": 1768,
                                    "y": 1004,
                                    "h": 111,
                                    "w": 111
                                },
                                {
                                    "x": 1483,
                                    "y": 1184,
                                    "h": 160,
                                    "w": 160
                                }
                            ]
                        }
                    },
                    "sizes": {
                        "large": {
                            "h": 1152,
                            "w": 2048,
                            "resize": "fit"
                        },
                        "medium": {
                            "h": 675,
                            "w": 1200,
                            "resize": "fit"
                        },
                        "small": {
                            "h": 383,
                            "w": 680,
                            "resize": "fit"
                        },
                        "thumb": {
                            "h": 150,
                            "w": 150,
                            "resize": "crop"
                        }
                    },
                    "original_info": {
                        "height": 1890,
                        "width": 3360,
                        "focus_rects": [
                            {
                                "x": 0,
                                "y": 8,
                                "w": 3360,
                                "h": 1882
                            },
                            {
                                "x": 0,
                                "y": 0,
                                "w": 1890,
                                "h": 1890
                            },
                            {
                                "x": 0,
                                "y": 0,
                                "w": 1658,
                                "h": 1890
                            },
                            {
                                "x": 0,
                                "y": 0,
                                "w": 945,
                                "h": 1890
                            },
                            {
                                "x": 0,
                                "y": 0,
                                "w": 3360,
                                "h": 1890
                            }
                        ]
                    },
                    "allow_download_status": {
                        "allow_download": True
                    },
                    "media_results": {
                        "result": {
                            "media_key": "3_1917461313573531648"
                        }
                    }
                }
            ]
        },
        "card": {},
        "place": {},
        "entities": {
            "hashtags": [
                {
                    "indices": [
                        130,
                        137
                    ],
                    "text": "MEDEZE"
                },
                {
                    "indices": [
                        138,
                        143
                    ],
                    "text": "IFCG"
                },
                {
                    "indices": [
                        144,
                        151
                    ],
                    "text": "NKCell"
                }
            ],
            "media": [
                {
                    "display_url": "pic.x.com/ptcq7GZY0l",
                    "expanded_url": "https://x.com/SuccessChTv/status/1917461328773669152/photo/1",
                    "id_str": "1917461313573531648",
                    "indices": [
                        152,
                        175
                    ],
                    "media_key": "3_1917461313573531648",
                    "media_url_https": "https://pbs.twimg.com/media/Gpww59IasAA69G8.jpg",
                    "type": "photo",
                    "url": "https://t.co/ptcq7GZY0l",
                    "ext_media_availability": {
                        "status": "Available"
                    },
                    "features": {
                        "large": {
                            "faces": [
                                {
                                    "x": 87,
                                    "y": 159,
                                    "h": 71,
                                    "w": 71
                                },
                                {
                                    "x": 1077,
                                    "y": 611,
                                    "h": 67,
                                    "w": 67
                                },
                                {
                                    "x": 903,
                                    "y": 721,
                                    "h": 97,
                                    "w": 97
                                }
                            ]
                        },
                        "medium": {
                            "faces": [
                                {
                                    "x": 51,
                                    "y": 93,
                                    "h": 42,
                                    "w": 42
                                },
                                {
                                    "x": 631,
                                    "y": 358,
                                    "h": 39,
                                    "w": 39
                                },
                                {
                                    "x": 529,
                                    "y": 422,
                                    "h": 57,
                                    "w": 57
                                }
                            ]
                        },
                        "small": {
                            "faces": [
                                {
                                    "x": 29,
                                    "y": 53,
                                    "h": 23,
                                    "w": 23
                                },
                                {
                                    "x": 357,
                                    "y": 203,
                                    "h": 22,
                                    "w": 22
                                },
                                {
                                    "x": 300,
                                    "y": 239,
                                    "h": 32,
                                    "w": 32
                                }
                            ]
                        },
                        "orig": {
                            "faces": [
                                {
                                    "x": 144,
                                    "y": 262,
                                    "h": 118,
                                    "w": 118
                                },
                                {
                                    "x": 1768,
                                    "y": 1004,
                                    "h": 111,
                                    "w": 111
                                },
                                {
                                    "x": 1483,
                                    "y": 1184,
                                    "h": 160,
                                    "w": 160
                                }
                            ]
                        }
                    },
                    "sizes": {
                        "large": {
                            "h": 1152,
                            "w": 2048,
                            "resize": "fit"
                        },
                        "medium": {
                            "h": 675,
                            "w": 1200,
                            "resize": "fit"
                        },
                        "small": {
                            "h": 383,
                            "w": 680,
                            "resize": "fit"
                        },
                        "thumb": {
                            "h": 150,
                            "w": 150,
                            "resize": "crop"
                        }
                    },
                    "original_info": {
                        "height": 1890,
                        "width": 3360,
                        "focus_rects": [
                            {
                                "x": 0,
                                "y": 8,
                                "w": 3360,
                                "h": 1882
                            },
                            {
                                "x": 0,
                                "y": 0,
                                "w": 1890,
                                "h": 1890
                            },
                            {
                                "x": 0,
                                "y": 0,
                                "w": 1658,
                                "h": 1890
                            },
                            {
                                "x": 0,
                                "y": 0,
                                "w": 945,
                                "h": 1890
                            },
                            {
                                "x": 0,
                                "y": 0,
                                "w": 3360,
                                "h": 1890
                            }
                        ]
                    },
                    "allow_download_status": {
                        "allow_download": True
                    },
                    "media_results": {
                        "result": {
                            "media_key": "3_1917461313573531648"
                        }
                    }
                }
            ],
            "symbols": [],
            "timestamps": [],
            "urls": [
                {
                    "display_url": "successchannel.co/post_news?post…",
                    "expanded_url": "https://successchannel.co/post_news?post_id=34506",
                    "url": "https://t.co/msEyj8s12P",
                    "indices": [
                        104,
                        127
                    ]
                }
            ],
            "user_mentions": []
        },
        "isRetweet": False,
        "isQuote": False,
        "media": [
            "https://pbs.twimg.com/media/Gpww59IasAA69G8.jpg"
        ],
        "isConversationControlled": False
    }

        # Act
        result = transformer.transform(input_data)

        # Assert
        expected_output = {
            "id": "1917461328773669152",
            "timestamp": pendulum.datetime(2025, 4, 30, 6, 9, 40, tz="UTC"),
            "content": "MEDEZE ให้การต้อนรับกลุ่มลูกค้า IFCG ให้ข้อมูลด้าน NK Cell เพื่อส่งเสริมองค์ความรู้ทางการแพทย์เชิงลึก   https://t.co/msEyj8s12P   #MEDEZE #IFCG #NKCell https://t.co/ptcq7GZY0l",
            "hashtags": ["#MEDEZE", "#IFCG", "#NKCell"],  # assuming TwitterEntities.hashtags is a list of Hashtag(text=...)
            "url": "https://x.com/SuccessChTv/status/1917461328773669152",
            "author_name": "SuccessChTv",  # assuming FacebookAuthor model has a `name` field
            "author_id": "1326006836017815560",
            "like_count": 15,
            "comment_count": 14,
            "retweet_count": 2,
            "quote_count": 7,
            "view_count": 17,
            "bookmark_count": 3,
            "engagement_count": 41,
        }
        assert_that(result).contains_key(
            "id",
            "timestamp",
            "content",
            "hashtags",
            "url",
            "author_name",
            "author_id",
            "like_count",
            "comment_count",
            "retweet_count",
            "quote_count",
            "view_count",
            "bookmark_count",
            "engagement_count",
        )
        assert_that(result["id"]).is_equal_to("1917461328773669152")
        assert_that(result["timestamp"]).is_equal_to(
            pendulum.datetime(2025, 4, 30, 6, 9, 40, tz="UTC")
            )
        assert_that(result["content"]).is_equal_to(
            "MEDEZE ให้การต้อนรับกลุ่มลูกค้า IFCG ให้ข้อมูลด้าน NK Cell เพื่อส่งเสริมองค์ความรู้ทางการแพทย์เชิงลึก   https://t.co/msEyj8s12P   #MEDEZE #IFCG #NKCell https://t.co/ptcq7GZY0l"
        )
        assert_that(result["hashtags"]).is_equal_to(["#MEDEZE", "#IFCG", "#NKCell"])
        assert_that(result["url"]).is_equal_to(
            "https://x.com/SuccessChTv/status/1917461328773669152"
        )
        assert_that(result["author_name"]).is_equal_to("SuccessChTv")
        assert_that(result["author_id"]).is_equal_to("1326006836017815560")
        assert_that(result["like_count"]).is_equal_to(15)
        assert_that(result["comment_count"]).is_equal_to(14)
        assert_that(result["retweet_count"]).is_equal_to(2)
        assert_that(result["quote_count"]).is_equal_to(7)
        assert_that(result["view_count"]).is_equal_to(17)
        assert_that(result["bookmark_count"]).is_equal_to(3)
        assert_that(result["engagement_count"]).is_equal_to(41)
        assert_that(result).is_equal_to(expected_output)


class TestTikTokTransformers:
    def test_tiktok_post_transformer(self):
        # Arrange
        transformer = TikTokPostOutputTransformer()
        input_data =     {
        "id": "7500994610081058070",
        "title": "Discover one of the essential app(s) for success and financial growth.\nTransform your life and unlock your potential today.💎✨ #JIFU #app #success #dedication #financial #markets #growth #unlockyou #transform #fyp #foryou #foryoupage ",
        "textLanguage": "en",
        "views": 179,
        "likes": 14,
        "comments": 7,
        "shares": 3,
        "bookmarks":2,
        "hashtags": [
            "jifu",
            "app",
            "success",
            "dedication",
            "financial",
            "markets",
            "growth",
            "unlockyou",
            "transform",
            "fyp",
            "foryou",
            "foryoupage"
        ],
        "channel": {
            "name": "Begin Your Success Journey 💎",
            "username": "khloe_langg",
            "id": "7263563347890144289",
            "url": "https://www.tiktok.com/@khloe_langg",
            "avatar": "https://p16-pu-no.tiktokcdn-eu.com/tos-no1a-avt-0068c001-no/c6a1095c25da61b54ba384510fbeca4c~tplv-tiktokx-cropcenter-q:1080:1080:q70.heic?dr=9608&idc=no1a&ps=87d6e48a&s=SEARCH&sc=avatar&shcp=c1333099&shp=45126217&t=223449c4",
            "verified": False,
            "followers": 617,
            "following": 2302,
            "videos": None,
            "region": None
        },
        "uploadedAt": 1746461408,
        "uploadedAtFormatted": "2025-05-05T16:10:08.000Z",
        "video": {
            "width": 576,
            "height": 1024,
            "ratio": "540p",
            "duration": 76.023,
            "url": "https://v45.tiktokcdn-eu.com/aa38233c1068bc389b2da4541ec37765/681b2f65/video/tos/no1a/tos-no1a-ve-0068c001-no/oY2AVQyB0A4wi87iiFNH9AIRCCxYfDDwuIEvAm/?a=1233&bti=NTY6QGo0QHM6OjZANDQuYCMucCMxNDNg&ch=0&cr=13&dr=0&er=0&lr=all&net=0&cd=0%7C0%7C0%7C&cv=1&br=1120&bt=560&cs=0&ds=6&ft=pfEtGMvt8Zmo04z0uI4jVUPF461rKsd.&mime_type=video_mp4&qs=0&rc=aGQ7Zjg2OztlaTxpaGYzPEBpMzU6dWo5cm1oMzMzbzczNUA1MjBgLzAvXi8xYl4vYDU2YSNva2o1MmRjLWBhLS1kMTFzcw%3D%3D&vvpl=1&l=20250506095952FD87270FB544A9B3FB11&btag=e00095000",
            "cover": "https://p16-pu-sign-no.tiktokcdn-eu.com/tos-no1a-p-0037-no/okIB0DA2KpqHCBgP3UFCipAAiAStYAAEtwfiI4~tplv-tiktokx-cropcenter:500:800.jpeg?biz_tag=musically_video.video_cover&dr=10397&idc=no1a&ps=13740610&refresh_token=9bd627e8&shcp=9b759fb9&shp=c1333099&t=4d5b0474&x-expires=1746543600&x-signature=fHYXB3zRyhlrBzGX3oDTWqKi1EM%3D",
            "thumbnail": "https://p16-pu-sign-no.tiktokcdn-eu.com/tos-no1a-p-0037-no/okIB0DA2KpqHCBgP3UFCipAAiAStYAAEtwfiI4~tplv-tiktokx-cropcenter:500:800.jpeg?biz_tag=musically_video.video_cover&dr=10397&idc=no1a&ps=13740610&refresh_token=9bd627e8&shcp=9b759fb9&shp=c1333099&t=4d5b0474&x-expires=1746543600&x-signature=fHYXB3zRyhlrBzGX3oDTWqKi1EM%3D"
        },
        "song": {
            "id": "7500994640624339734",
            "title": "original sound - khloe_langg",
            "artist": "Begin Your Success Journey 💎",
            "album": None,
            "duration": 76,
            "cover": "https://p16-pu-no.tiktokcdn-eu.com/tos-no1a-avt-0068c001-no/c6a1095c25da61b54ba384510fbeca4c~tplv-tiktokx-cropcenter-q:1080:1080:q70.heic?dr=9608&idc=no1a&ps=87d6e48a&s=SEARCH&sc=avatar&shcp=c1333099&shp=45126217&t=223449c4"
        },
        "subtitleInformation": None,
        "postPage": "https://www.tiktok.com/@khloe_langg/video/7500994610081058070",
        "keyword": "medeze"
    }

        # Act
        result = transformer.transform(input_data)

        # Assert
        expected_output = {
            "id": "7500994610081058070",
            "timestamp": pendulum.datetime(2025, 5, 5, 16, 10, 8, tz="UTC"),
            "content": "Discover one of the essential app(s) for success and financial growth. Transform your life and unlock your potential today.💎✨ #JIFU #app #success #dedication #financial #markets #growth #unlockyou #transform #fyp #foryou #foryoupage ",
            "url": "https://www.tiktok.com/@khloe_langg/video/7500994610081058070",
            "hashtags": [
                "jifu",
                "app",
                "success",
                "dedication",
                "financial",
                "markets",
                "growth",
                "unlockyou",
                "transform",
                "fyp",
                "foryou",
                "foryoupage"
            ],
            "author_name": "Begin Your Success Journey 💎",
            "author_username": "khloe_langg",
            "author_id": "7263563347890144289",
            "like_count": 14,
            "comment_count": 7 ,
            "view_count": 179,
            "share_count": 3,
            "bookmark_count": 2,
            "engagement_count": 24,
        }
        assert_that(result).contains_key(
            "id",
            "timestamp",
            "content",
            "url",
            "hashtags",
            "author_name",
            "author_username",
            "author_id",
            "like_count",
            "comment_count",
            "view_count",
            "share_count",
            "bookmark_count",
            "engagement_count",
        )
        assert_that(result["id"]).is_equal_to("7500994610081058070")
        assert_that(result["timestamp"]).is_equal_to(
            pendulum.datetime(2025, 5, 5, 16, 10, 8, tz="UTC")
        )
        assert_that(result["content"]).is_equal_to(
            "Discover one of the essential app(s) for success and financial growth. Transform your life and unlock your potential today.💎✨ #JIFU #app #success #dedication #financial #markets #growth #unlockyou #transform #fyp #foryou #foryoupage "
        )
        assert_that(result["url"]).is_equal_to(
            "https://www.tiktok.com/@khloe_langg/video/7500994610081058070"
        )
        assert_that(result["hashtags"]).is_equal_to(
            [
                "jifu",
                "app",
                "success",
                "dedication",
                "financial",
                "markets",
                "growth",
                "unlockyou",
                "transform",
                "fyp",
                "foryou",
                "foryoupage"
            ]
        )
        assert_that(result["author_name"]).is_equal_to("Begin Your Success Journey 💎")
        assert_that(result["author_username"]).is_equal_to("khloe_langg")
        assert_that(result["author_id"]).is_equal_to("7263563347890144289")
        assert_that(result["like_count"]).is_equal_to(14)
        assert_that(result["comment_count"]).is_equal_to(7)
        assert_that(result["view_count"]).is_equal_to(179)
        assert_that(result["share_count"]).is_equal_to(3)
        assert_that(result["bookmark_count"]).is_equal_to(2)
        assert_that(result["engagement_count"]).is_equal_to(24)
        assert_that(result).is_equal_to(expected_output)

    def test_tiktok_comment_transformer(self):
        transformer = TikTokCommentOutputTransformer()
        input_data =     {
            "id": "7501165117524443924",
            "text": "ทำเป็นวัยรุ่นเลยนะอกหักแล้วต้องตัดผมสั้น😁😁😁😁",
            "createdAt": "2025-05-06T03:12:09.000Z",
            "likeCount": 8,
            "replyCount": 1,
            "commentLanguage": "th",
            "awemeId": "7501146411497458951",
            "isAuthorLiked": True,
            "user": {
                "id": "7281068405791769606",
                "username": "user4377810286660",
                "displayName": "ยุทธ บางหวาย",
                "url": "https://www.tiktok.com/@user4377810286660",
                "bio": "",
                "avatarUrl": "https://p77-sg.tiktokcdn.com/tos-alisg-avt-0068/3018ffe2193b3cd3207133d862a02452~tplv-tiktokx-cropcenter-q:100:100:q70.heic?dr=9606&idc=no1a&ps=87d6e48a&s=COMMENT_LIST&sc=avatar&shcp=ff37627b&shp=30310797&t=223449c4",
                "region": "TH",
                "language": "th",
                "hasEmail": False,
                "hasPhone": False,
                "coverImage": None
            },
            "search_term": "https://www.tiktok.com/@je_31/video/7501169801826422023,https://www.tiktok.com/@nudarin693/video/7501146411497458951,https://www.tiktok.com/@nuknikguide/video/7499749430099299591,https://www.tiktok.com/@kesjirakhwanthong/video/7499653332420660488"
        }

        # Act
        result = transformer.transform(input_data)

        # Assert
        expected_output = {
            "id": "7501165117524443924",
            "timestamp": pendulum.datetime(2025, 5, 6, 3, 12, 9, tz="UTC"),
            "content": "ทำเป็นวัยรุ่นเลยนะอกหักแล้วต้องตัดผมสั้น😁😁😁😁",
            "like_count": 8,
            "reply_count": 1,
            "author_name": "user4377810286660",
            "author_id": "7281068405791769606",
            "engagement_count": 9,
        }
        assert_that(result).contains_key(
            "id",
            "timestamp",
            "content",
            "like_count",
            "reply_count",
            "author_name",
            "author_id",
            "engagement_count",
        )
        assert_that(result["id"]).is_equal_to("7501165117524443924")
        assert_that(result["timestamp"]).is_equal_to(
            pendulum.datetime(2025, 5, 6, 3, 12, 9, tz="UTC")
        )
        assert_that(result["content"]).is_equal_to(
            "ทำเป็นวัยรุ่นเลยนะอกหักแล้วต้องตัดผมสั้น😁😁😁😁"
        )
        assert_that(result["like_count"]).is_equal_to(8)
        assert_that(result["reply_count"]).is_equal_to(1)
        assert_that(result["author_name"]).is_equal_to("user4377810286660")
        assert_that(result["author_id"]).is_equal_to("7281068405791769606")
        assert_that(result["engagement_count"]).is_equal_to(9)
        assert_that(result).is_equal_to(expected_output)



class TestGetTransformer:
    def test_get_transformer_returns_correct_transformer(self):
        # Test all transformer types
        transformers = {
            "facebook-post": FacebookPostOutputTransformer,
            "facebook-comment": FacebookCommentOutputTransformer,
            "instagram-post": InstagramPostOutputTransformer,
            "instagram-comment": InstagramCommentOutputTransformer,
            "youtube-post": YoutubePostOutputTransformer,
            "youtube-comment": YoutubeCommentOutputTransformer,
            "twitter": TwitterOutputTransformer,
            "tiktok-post": TikTokPostOutputTransformer,
            "tiktok-comment": TikTokCommentOutputTransformer,
        }

        for actor_name, transformer_class in transformers.items():
            transformer = get_transformer(actor_name)
            assert_that(transformer).is_instance_of(transformer_class)

    def test_get_transformer_raises_error_for_invalid_actor(self):
        with pytest.raises(ValueError) as excinfo:
            get_transformer("invalid-actor")
        assert_that(str(excinfo.value)).contains("Invalid actor name")
