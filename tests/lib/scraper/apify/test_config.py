import pytest
from datetime import datetime
import pendulum
from assertpy import assert_that
from lib.scraper.apify.config import (
    FacebookPostInputBuilder,
    FacebookCommentInputBuilder,
    InstagramPostInputBuilder,
    InstagramCommentInputBuilder,
    YoutubePostInputBuilder,
    YoutubeCommentInputBuilder,
    TwitterPostInputBuilder,
    TwitterCommentInputBuilder,
    TikTokPostInputBuilder,
    TikTokCommentInputBuilder,
    get_actor_config,
)


@pytest.fixture
def sample_search_items():
    return ["test1", "test2", "test3"]


@pytest.fixture
def mock_pendulum_now(monkeypatch):
    """Mock pendulum.now() to return a fixed date."""

    class MockPendulum:
        @staticmethod
        def now():
            return pendulum.datetime(2024, 1, 1)

        @staticmethod
        def to_date_string():
            return "2024-01-01"

        @staticmethod
        def subtract(days):
            return pendulum.datetime(2023, 12, 29)

    monkeypatch.setattr(pendulum, "now", MockPendulum.now)
    return MockPendulum


def test_facebook_input_builder(sample_search_items, mock_pendulum_now):
    max_posts = 5
    days_lookback = 3
    builder = FacebookPostInputBuilder(max_posts=max_posts, days_lookback=days_lookback)
    search_terms = builder.create_search_term(sample_search_items)
    assert_that(search_terms).is_type_of(list)
    assert_that(search_terms).is_length(len(sample_search_items))

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "query": "test1",
        "start_date": "2023-12-29",
        "end_date": "2024-01-01",
        "max_posts": 5,
        "search_type": "posts",
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_facebook_comment_input_builder(sample_search_items):
    builder = FacebookCommentInputBuilder()
    search_terms = builder.create_search_term(sample_search_items)
    expect_search_terms = ["test1,test2,test3"]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "startUrls": [
            {"url": "test1", "method": "GET"},
            {"url": "test2", "method": "GET"},
            {"url": "test3", "method": "GET"},
        ],
        "includeNestedComments": True,
        "resultsLimit": 15000,
        "viewOption": "RANKED_UNFILTERED",
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_instagram_input_builder(sample_search_items, mock_pendulum_now):
    builder = InstagramPostInputBuilder()
    search_terms = builder.create_search_term(sample_search_items)
    expect_search_terms = [
        "https://www.instagram.com/explore/tags/test1,https://www.instagram.com/explore/tags/test2,https://www.instagram.com/explore/tags/test3",
    ]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "startUrls": [
            "https://www.instagram.com/explore/tags/test1",
            "https://www.instagram.com/explore/tags/test2",
            "https://www.instagram.com/explore/tags/test3",
        ],
        "until": "2023-12-17",  # 15 days before 2024-01-01
        "maxItems": 1000,
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_instagram_comment_input_builder(sample_search_items):
    builder = InstagramCommentInputBuilder()
    search_terms = builder.create_search_term(sample_search_items)
    expect_search_terms = ["test1,test2,test3"]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "startUrls": ["test1", "test2", "test3"],
        "maxComments": 1000,
        "includeReplies": True,
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_youtube_input_builder(sample_search_items):
    builder = YoutubePostInputBuilder()
    search_terms = builder.create_search_term(sample_search_items)
    expect_search_terms = ['"test1","test2","test3"']
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "searchQueries": ['"test1"', '"test2"', '"test3"'],
        "dateFilter": "week",
        "sortingOrder": "date",
        "maxResultStreams": 50,
        "maxResults": 50,
        "maxResultsShorts": 50,
        "downloadSubtitles": False,
        "hasCC": False,
        "hasLocation": False,
        "hasSubtitles": False,
        "is360": False,
        "is3D": False,
        "is4K": False,
        "isBought": False,
        "isHD": False,
        "isHDR": False,
        "isLive": False,
        "isVR180": False,
        "preferAutoGeneratedSubtitles": False,
        "saveSubsToKVS": False,
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_youtube_comment_input_builder(sample_search_items):
    builder = YoutubeCommentInputBuilder()
    search_terms = builder.create_search_term(sample_search_items)
    expect_search_terms = ["test1,test2,test3"]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "startUrls": [
            {"url": "test1", "method": "GET"},
            {"url": "test2", "method": "GET"},
            {"url": "test3", "method": "GET"},
        ],
        "commentsSortBy": "1",
        "maxComments": 1000,
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_twitter_input_builder(sample_search_items, mock_pendulum_now):
    builder = TwitterPostInputBuilder()
    search_terms = builder.create_search_term(sample_search_items)
    expect_search_terms = ['"test1" OR "test2" OR "test3"']
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "searchTerms": ['"test1" OR "test2" OR "test3"'],
        "start": "2023-12-29",  # 3 days before 2024-01-01
        "maxItems": 800,
        "sort": "Latest",
    }
    assert_that(input_data).is_equal_to(expect_input_data)

    builder = TwitterCommentInputBuilder()
    search_terms = builder.create_search_term(sample_search_items)
    expect_search_terms = [
        "conversation_id:test1,conversation_id:test2,conversation_id:test3"
    ]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "searchTerms": [
            "conversation_id:test1",
            "conversation_id:test2",
            "conversation_id:test3",
        ],
        "start": "2023-12-29",  # 3 days before 2024-01-01
        "maxItems": 800,
        "sort": "Latest",
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_tiktok_input_builder(sample_search_items):
    builder = TikTokPostInputBuilder()
    search_terms = builder.create_search_term(sample_search_items)
    expect_search_terms = ["test1,test2,test3"]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "keywords": ["test1", "test2", "test3"],
        "dateRange": "THIS_WEEK",
        "includeSearchKeywords": True,
        "maxItems": 1000,
        "sortType": "DATE_POSTED",
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_tiktok_comment_input_builder(sample_search_items):
    builder = TikTokCommentInputBuilder()
    search_terms = builder.create_search_term(sample_search_items)
    expect_search_terms = ["test1,test2,test3"]
    assert_that(search_terms).is_equal_to(expect_search_terms)

    input_data = builder.build(search_terms[0])
    expect_input_data = {
        "startUrls": ["test1", "test2", "test3"],
        "includeReplies": True,
        "maxItems": 1000,
    }
    assert_that(input_data).is_equal_to(expect_input_data)


def test_get_actor_config():
    # Test valid actor
    actor_id, memory, input_builder = get_actor_config("facebook-post")
    assert_that(actor_id).is_equal_to("danek/facebook-search-ppr")
    assert_that(memory).is_equal_to(1024)
    assert_that(input_builder).is_instance_of(FacebookPostInputBuilder)

    # Test invalid actor
    assert_that(lambda: get_actor_config("invalid-actor")).raises(ValueError)


def test_empty_search_items():
    empty_items = []
    builders = [
        FacebookPostInputBuilder(),
        FacebookCommentInputBuilder(),
        InstagramPostInputBuilder(),
        InstagramCommentInputBuilder(),
        YoutubePostInputBuilder(),
        YoutubeCommentInputBuilder(),
        TwitterPostInputBuilder(),
        TikTokPostInputBuilder(),
        TikTokCommentInputBuilder(),
    ]

    for builder in builders:
        search_terms = builder.create_search_term(empty_items)
        assert_that(search_terms).is_type_of(list)
        assert_that(search_terms).is_empty()
