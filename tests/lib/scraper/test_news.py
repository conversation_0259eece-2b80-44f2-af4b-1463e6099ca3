import pytest
import pendulum
from contextlib import nullcontext as does_not_raise
from lib.scraper.news import NewsScraper


@pytest.fixture
def news_scraper():
    """Create a NewsScraper instance for testing."""
    return NewsScraper(api_key="test_key", keywords=["test"])


@pytest.fixture
def mock_now(monkeypatch):
    """Mock pendulum.now() to return a fixed date."""
    fixed_now = pendulum.datetime(2024, 5, 15, 12, 0, 0)

    def mock_pendulum_now():
        return fixed_now

    monkeypatch.setattr(pendulum, "now", mock_pendulum_now)
    return fixed_now


@pytest.mark.parametrize(
    "date_str,expected,expectation",
    [
        # Relative dates
        ("1 hour ago", pendulum.datetime(2024, 5, 15, 11, 0, 0), does_not_raise()),
        ("2 hours ago", pendulum.datetime(2024, 5, 15, 10, 0, 0), does_not_raise()),
        ("1 day ago", pendulum.datetime(2024, 5, 14, 12, 0, 0), does_not_raise()),
        ("3 days ago", pendulum.datetime(2024, 5, 12, 12, 0, 0), does_not_raise()),
        ("1 week ago", pendulum.datetime(2024, 5, 8, 12, 0, 0), does_not_raise()),
        ("2 weeks ago", pendulum.datetime(2024, 5, 1, 12, 0, 0), does_not_raise()),
        ("1 month ago", pendulum.datetime(2024, 4, 15, 12, 0, 0), does_not_raise()),
        ("3 months ago", pendulum.datetime(2024, 2, 15, 12, 0, 0), does_not_raise()),
        ("1 year ago", pendulum.datetime(2023, 5, 15, 12, 0, 0), does_not_raise()),
        # Absolute dates
        ("2024-01-15", pendulum.datetime(2024, 1, 15, 0, 0, 0), does_not_raise()),
        # Invalid formats should return current time
        ("May 10, 2024", pendulum.datetime(2024, 5, 15, 12, 0, 0), does_not_raise()),
        ("invalid date", pendulum.datetime(2024, 5, 15, 12, 0, 0), does_not_raise()),
        ("yesterday", pendulum.datetime(2024, 5, 15, 12, 0, 0), does_not_raise()),
        ("", pendulum.datetime(2024, 5, 15, 12, 0, 0), does_not_raise()),
    ],
)
def test_format_date(news_scraper, mock_now, date_str, expected, expectation):
    """Test the _format_date method with various date formats."""
    with expectation:
        result = news_scraper._format_date(date_str)
        assert result == expected, f"Failed to parse '{date_str}'"
