import pytest
from assertpy import assert_that
from lib.scraper.apify.config import TwitterInputBuilder


class TestTwitterInputBuilder:
    @pytest.fixture
    def builder(self):
        return TwitterInputBuilder()

    def test_create_search_term_with_multiple_keywords(self, builder):
        # Arrange
        search_items = ["medeze", "stem cell"]

        # Act
        result = builder.create_search_term(search_items)

        # Assert
        assert_that(result).is_equal_to(['"medeze" OR "stem cell"'])

    def test_create_search_term_with_single_keyword(self, builder):
        # Arrange
        search_items = ["medeze"]

        # Act
        result = builder.create_search_term(search_items)

        # Assert
        assert_that(result).is_equal_to(['"medeze"'])

    def test_create_search_term_with_empty_list(self, builder):
        # Arrange
        search_items = []

        # Act
        result = builder.create_search_term(search_items)

        # Assert
        assert_that(result).is_equal_to(list())
