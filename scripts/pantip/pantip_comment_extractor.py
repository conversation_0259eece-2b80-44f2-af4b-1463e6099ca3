from playwright.sync_api import sync_playwright


def extract_pantip_comment(url, comment_no=42):
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        page = browser.new_page()

        # Navigate to the Pantip post URL
        print(url)
        page.goto(url)

        # Wait for the specific comment to be visible
        comment_selector = (
            f"div.display-post-wrapper:has-text('ความคิดเห็นที่ {comment_no}')"
        )
        page.wait_for_selector(comment_selector, state="visible")

        # Locate the comment container
        comment = page.locator(comment_selector)

        # Extract comment ID from the div id attribute
        comment_id = comment.get_attribute("id")
        if comment_id:
            comment_id = comment_id.replace("comment-", "")

        # Extract comment text
        comment_text = comment.locator("div.display-post-story").text_content().strip()

        # Extract like score
        like_score = comment.locator("span.like-score").text_content().strip()

        # Extract emotion score
        emotion_score = comment.locator("span.emotion-score").text_content().strip()

        # Extract author information
        author_element = comment.locator("a.display-post-name")
        author_id = None

        # Get author ID from the profile URL
        author_profile_url = author_element.get_attribute("href")
        if author_profile_url:
            # Extract ID from URL like "/profile/177254"
            author_id = author_profile_url.split("/")[-1]

        author_name = author_element.text_content().strip()

        # Extract date from the abbr element
        date_element = comment.locator("span.display-post-timestamp abbr")
        date = date_element.get_attribute("data-utime")

        browser.close()

        return {
            "comment_id": comment_id,
            "comment_no": comment_no,
            "comment_text": comment_text,
            "like_score": like_score,
            "emotion_score": emotion_score,
            "author_id": author_id,
            "author_name": author_name,
            "date": date,
        }


if __name__ == "__main__":
    # Example Pantip URL - replace with actual URL
    url = "https://pantip.com/topic/43321543/comment42"
    comment_data = extract_pantip_comment(url, 42)

    print(f"Comment ID: {comment_data['comment_id']}")
    print(f"Comment No: {comment_data['comment_no']}")
    print(f"Comment Text: {comment_data['comment_text']}")  # Show first 100 chars
    print(f"Like Score: {comment_data['like_score']}")
    print(f"Emotion Score: {comment_data['emotion_score']}")
    print(f"Author ID: {comment_data['author_id']}")
    print(f"Author Name: {comment_data['author_name']}")
    print(f"Date: {comment_data['date']}")
