from playwright.sync_api import sync_playwright
import re

base_url = "https://search.pantip.com"


def extract_numbered_sections(html):
    # Split based on number prefix like "1:&nbsp;", "2:&nbsp;", etc.
    parts = re.split(r"(\d+):\s*&nbsp;?", html)
    result = {}
    for i in range(1, len(parts), 2):
        number = parts[i]
        content = parts[i + 1]
        links = re.findall(r'<a[^>]*href="([^"]+)"[^>]*>(.*?)</a>', content)
        result[number] = format_links_to_json(links)
    return result


def format_links_to_json(links_list):
    post_link = None
    comments = list()

    for link, text in links_list:
        # Clean the text
        text = text.strip().replace("&nbsp;", "")

        # Check if it's a comment or a topic
        if text.startswith("#"):
            # It's a comment
            comment_number = text.replace("#", "")
            comments.append({comment_number: link})
        else:
            # It's a topic
            post_link = link
            is_medeze = is_medeze_topic(text)

    post_info = {
        "link": post_link,
        "comments": comments,
        "is_medeze_topic": is_medeze,
    }

    return post_info


def is_medeze_topic(text):
    return "medeze" in text.lower()


with sync_playwright() as p:
    browser = p.chromium.launch(headless=True)
    page = browser.new_page()
    page.goto("https://search.pantip.com/ss?q=medeze", timeout=60000)

    # Target the specific paragraph section
    selector = "#ss-form > table > tbody > tr:nth-child(3) > td > table > tbody > tr:nth-child(2) > td:nth-child(1) > p"
    page.wait_for_selector(selector)

    # Extract raw HTML content
    html = page.locator(selector).inner_html()

    # Parse and separate
    result = extract_numbered_sections(html)

    # Print the structured result
    for key, items in result.items():
        print(f"{key} : {items}")

    browser.close()
