from playwright.sync_api import sync_playwright


def extract_pantip_post_data(url):
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()

        # Navigate to the Pantip post URL
        page.goto(url)

        # Wait for the main content to load
        page.wait_for_selector("div.display-post-wrapper.main-post", state="visible")

        # Extract topic ID from URL or div id
        topic = page.locator("div.display-post-wrapper.main-post")
        topic_id = topic.get_attribute("id").replace("topic-", "")

        # Extract post title
        post_title = topic.locator("h2.display-post-title").text_content().strip()

        # Extract post text
        post_text = topic.locator("div.display-post-story").text_content().strip()

        # Extract images from the post
        image_elements = topic.locator("div.display-post-story img")
        image_urls = []

        # Get count of images
        image_count = image_elements.count()

        # Extract each image URL
        for i in range(image_count):
            img = image_elements.nth(i)
            src = img.get_attribute("src")
            if src:
                image_urls.append(src)

        # Extract post date
        post_date_element = topic.locator("span.display-post-timestamp abbr")
        post_date = post_date_element.get_attribute("data-utime")

        # Extract like score
        like_score = topic.locator("span.like-score").text_content().strip()

        # Extract emotion score
        emotion_score = topic.locator("span.emotion-score").text_content().strip()

        # Extract author ID and name
        author_element = topic.locator("a.display-post-name.owner")
        author_id = author_element.get_attribute("id")
        author_name = author_element.text_content().strip()

        browser.close()

        return {
            "topic_id": topic_id,
            "post_title": post_title,
            "post_text": post_text,
            "post_date": post_date,
            "like_score": like_score,
            "emotion_score": emotion_score,
            "author_id": author_id,
            "author_name": author_name,
        }


if __name__ == "__main__":
    # Example Pantip URL
    url = "https://pantip.com/topic/43321543"
    post_data = extract_pantip_post_data(url)

    print(f"Topic ID: {post_data['topic_id']}")
    print(f"Post Title: {post_data['post_title']}")
    print(f"Post Text: {post_data['post_text']}")  # Show first 100 chars
    print(f"Post Date: {post_data['post_date']}")
    print(f"Like Score: {post_data['like_score']}")
    print(f"Emotion Score: {post_data['emotion_score']}")
    print(f"Author ID: {post_data['author_id']}")
    print(f"Author Name: {post_data['author_name']}")
