#############################
# Youtube
#############################


class DescriptionLink(BaseModel):
    url: HttpUrl
    text: str


class YoutubePost(BaseModel):
    title: str
    type: str
    url: HttpUrl
    view_count: int = Field(0, alias = "viewCount")
    date: datetime.datetime
    likes: int
    channel_name: str = Field(..., alias = "channelName")
    channel_url: HttpUrl = Field(..., alias = "channelUrl")
    channel_username: str = Field(..., alias = "channelUsername")
    number_of_subscribers: int = Field(0, alias = "numberOfSubscribers")
    duration: str
    comments_count: int = Field(0, alias = "commentsCount")
    text: str
    description_links: List[DescriptionLink] = Field(..., alias = "descriptionLinks")
    subtitles: Optional[str]  # or Optional[Any] if it could be other types
    order: int
    comments_turned_off: bool = Field(..., alias = "commentsTurnedOff")
    from_yt_url: HttpUrl = Field(..., alias = "fromYTUrl")
    hashtags: List[str]


    @classmethod
    def parse(cls, data: dict):
        # Parse and convert timestamp to datetime
        return cls.model_validate(data)


class YoutubePostTransformer(ApifyOutputTransformer):
    def transform(self, data: dict) -> dict:
        """Transform the output data from the Apify actor."""
        post = YoutubePost.parse(data)
        parsed_data = {
            "title": post.title,
            "type": post.type,
            "url": str(post.url),
            "view_count": post.view_count,
            "date": post.date,
            "likes": post.likes,
            "channel_name": post.channel_name,
            "channel_url": post.channel_url,
            "channel_username": post.channel_username,
            "number_of_subscribers": post.number_of_subscribers,
            "duration": post.duration,
            "comments_count": post.comments_count,
            "text": post.text,
            "description_links": post.description_links,
            "subtitles": post.subtitles,
            "order": post.order,
            "comments_turned_off": post.comments_turned_off,
            "from_yt_url": str(post.from_yt_url),
            "hashtags": post.hashtags
        }
        return parsed_data