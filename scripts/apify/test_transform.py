import json
from lib.scraper.apify.transformer import get_transformer


def read_json_file(filename: str) -> list[dict]:
    """Read data from a JSON file."""
    with open(filename, "r", encoding="utf-8") as f:
        return json.load(f)


data = read_json_file("lib/scraper/apify/output_json/ig-comment.json")

for item in data:
    transformer = get_transformer("instagram-comment")
    print(transformer.transform(item))
