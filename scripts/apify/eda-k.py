from lib.scraper.apify.scraper import ApifyScraper
from lib.scraper.apify.transformer import get_transformer
import asyncio
from lib.common.config import SecretConfig
import json
from lib.common.logger import logger
import uuid

from lib.scraper.apify.config import get_actor_config


def write_json_file(data: list[dict], filename: str) -> None:
    """Write data to a JSON file."""
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=4)


def is_medeze_related(data: dict) -> bool:
    keywords = ["medeze", "เมดีซ"]
    content = data.get("content", "").lower()
    author = data.get("author_name", "").lower()
    url = data.get("url", "").lower()
    combined_text = f"{content} {author} {url}"
    return any(keyword in combined_text for keyword in keywords)


async def test_transformer(channel_filename: str):
    secret_config = SecretConfig()
    apify_token = secret_config.get_apify_token()

    # # set keywords to test
    keywords = ["MEDEZE", "เมดีซ", "เมดีซ กรุ๊ป", "นายแพทย์วีรพล เขมะรังสรรค์"]

    # # actor config
    # channel_filename = "instagram"
    actor_name = f"{channel_filename}-post"
    kwargs = {"max_items": 500, "days_lookback": 180}

    # # Run apify scraper
    search_items = keywords
    scraper = ApifyScraper(apify_token, actor_name, **kwargs)
    results = await scraper.run(search_items)
    write_json_file(results, f"dev/apify_eda/{channel_filename}_posts.json")

    ###### DONE INGESTION

    ###### POST TRANFORM

    # read raw
    with open(
        f"dev/apify_eda/{channel_filename}_posts.json", "r", encoding="utf-8"
    ) as f:
        results = json.load(f)

    logger.info(f"Found {len(results)} results")

    # transform
    transformer = get_transformer(f"{channel_filename}-post")
    results = [transformer.transform(item) for item in results]
    # Convert timestamp to string for JSON serialization
    for item in results:
        if "timestamp" in item and hasattr(item["timestamp"], "isoformat"):
            item["timestamp"] = item["timestamp"].isoformat()
    write_json_file(results, f"dev/apify_eda/{channel_filename}_posts_transformed.json")

    # De-duplicate
    unique_items = {item["id"]: item for item in results}
    results = list(unique_items.values())
    logger.info(f"Found {len(results)} unique results")
    write_json_file(results, f"dev/apify_eda/{channel_filename}_posts_unique.json")

    # filter relate
    results = [post for post in results if is_medeze_related(post)]
    logger.info(f"Found {len(results)} related results")
    write_json_file(results, f"dev/apify_eda/{channel_filename}_posts_related.json")

    ###### DONE POST TRANFORM

    ##### COMMENT INGEST
    # read raw
    with open(
        f"dev/apify_eda/{channel_filename}_posts_related.json", "r", encoding="utf-8"
    ) as f:
        results = json.load(f)

    # filter comment>0
    results = [post for post in results if post.get("comment_count", 0) > 0]
    logger.info(f"Found {len(results)} posts with comments")

    # Get links from posts_comment
    search_comment_links = [post.get("url", "") for post in results]
    comment_scraper = ApifyScraper(apify_token, f"{channel_filename}-comment")
    comment_results = await comment_scraper.run(search_comment_links)
    write_json_file(comment_results, f"dev/apify_eda/{channel_filename}_comments.json")
    ##### DONE COMMENT INGEST

    ##### COMMENT TRANSFORM
    # read raw
    with open(
        f"dev/apify_eda/{channel_filename}_comments.json", "r", encoding="utf-8"
    ) as f:
        comment_results = json.load(f)

    logger.info(f"Found {len(comment_results)} comment results")

    # transform
    transformer = get_transformer(f"{channel_filename}-comment")
    comment_results = [transformer.transform(item) for item in comment_results]
    # Convert timestamp to string for JSON serialization
    for item in comment_results:
        if "timestamp" in item and hasattr(item["timestamp"], "isoformat"):
            item["timestamp"] = item["timestamp"].isoformat()
    write_json_file(
        comment_results, f"dev/apify_eda/{channel_filename}_comments_transformed.json"
    )

    # filter relate
    # comment_results = [
    #     comment for comment in comment_results if is_medeze_related(comment)
    # ]
    # logger.info(f"Found {len(comment_results)} related comment results")


async def test_youtube_post():
    secret_config = SecretConfig()
    apify_token = secret_config.get_apify_token()

    # set keywords to test
    keywords = ["medeze", "เมดีซ", "stem cell", "NK cell"]
    # keywords = ["medeze"]

    # actor config
    actor_name = "youtube-post"
    kwargs = {"date_filter": "year"}
    search_items = keywords

    # Run apify scraper
    scraper = ApifyScraper(apify_token, actor_name, **kwargs)
    results = await scraper.run(search_items)
    # raw
    write_json_file(results, "dev/apify_eda/youtube_posts.json")

    ## Transform
    # read raw

    # filter comment>0

    # remove duplicate

    # filter relate

    # output transform before table

    # write json


async def fetch_test_data():
    keywords = [
        "Hair Renaissance"
    ]  # ["MEDEZE"]  # , "เมดีซ", "เมดีซ กรุ๊ป", "นายแพทย์วีรพล เขมะรังสรรค์"]
    post_actor_args = {
        "facebook-post": {"max_posts": 1000, "days_lookback": 180},
        "instagram-post": {"max_items": 1000, "days_lookback": 180},
        "youtube-post": {"max_results": 1000, "date_filter": "year"},
        "twitter": {"max_items": 1000, "days_lookback": 180},
        "tiktok-post": {"max_items": 1000, "date_range": "LAST_SIX_MONTHS"},
    }
    comment_actor = {
        "facebook-comment": {"results_limit": 15000},
        "instagram-comment": {"max_comments": 1000},
        "youtube-comment": {"max_comments": 1000},
        "tiktok-comment": {"max_items": 1000},
    }
    secret_config = SecretConfig()
    apify_token = secret_config.get_apify_token()
    search_items = keywords

    all_posts = []
    all_comments = []

    for k, v in post_actor_args.items():
        actor_name = k
        kwargs = v
        channel = actor_name.split("-")[0]
        scraper = ApifyScraper(apify_token, actor_name, **kwargs)
        results = await scraper.run(search_items)

        # transform
        transformer = get_transformer(actor_name)
        results = [transformer.transform(item) for item in results]
        # de-duplicate
        unique_items = {item["id"]: item for item in results}
        results = list(unique_items.values())
        # filter relate
        posts = [post for post in results if is_medeze_related(post)]
        logger.info(f"Found {len(results)} related results for {actor_name}")

        # create post object
        posts_list = [{"post": post["content"]} for post in posts]

        # get url where comment_count>0
        comment_links = [
            post.get("url", "") for post in posts if post.get("comment_count", 0) > 0
        ]
        # get comment
        actor = f"{channel}-comment"
        if actor in comment_actor:
            kwargs = comment_actor[actor]
            scraper = ApifyScraper(apify_token, actor, **kwargs)
            comment_results = await scraper.run(comment_links)
            transformer = get_transformer(actor)
            comment_results = [transformer.transform(item) for item in comment_results]

            # Map comments to their corresponding posts
            comments_list = []
            for comment in comment_results:
                post_id = comment.get("post_id")
                post = next((p for p in results if p["id"] == post_id), None)
                if post:
                    comment_obj = {
                        "post": post.get("content", ""),
                        "comment": comment["content"],
                    }
                    comments_list.append(comment_obj)
        write_json_file(posts_list, f"dev/apify_eda/hair/{channel}_posts.json")
        write_json_file(comments_list, f"dev/apify_eda/hair/{channel}_comments.json")

        all_posts.extend(posts_list)
        all_comments.extend(comments_list)

    write_json_file(all_posts, f"dev/apify_eda/hair/all_posts.json")
    write_json_file(all_comments, f"dev/apify_eda/hair/all_comments.json")

    # write_json_file(all_posts, "dev/apify_eda/all_posts.json")


if __name__ == "__main__":
    # print(get_actor_config("facebook-post", max_posts=500, days_lookback=30))
    # asyncio.run(test_facebook_post())
    asyncio.run(fetch_test_data())
    # asyncio.run(test_youtube_post())
