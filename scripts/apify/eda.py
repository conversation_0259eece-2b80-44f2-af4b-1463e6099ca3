from lib.scraper.apify.scraper import Apify<PERSON><PERSON>raper
import asyncio
from lib.common.config import SecretConfig
import json

from lib.scraper.apify.config import get_actor_config


def write_json_file(data: list[dict], filename: str) -> None:
    """Write data to a JSON file."""
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=4)


async def test_facebook_post():
    secret_config = SecretConfig()
    apify_token = secret_config.get_apify_token()

    # set keywords to test
    keywords = ["medeze", "เมดีซ", "stem cell", "NK cell"]
    # keywords = ["medeze"]

    # actor config
    actor_name = "facebook-post"
    kwargs = {"max_posts": 500, "days_lookback": 30}
    search_items = keywords

    # Run apify scraper
    scraper = ApifyScraper(apify_token, actor_name, **kwargs)
    results = await scraper.run(search_items)
    write_json_file(results, "dev/apify_eda/fb_posts.json")


async def test_youtube_post():
    secret_config = SecretConfig()
    apify_token = secret_config.get_apify_token()

    # set keywords to test
    keywords = ["medeze", "เมดีซ", "stem cell", "NK cell"]
    # keywords = ["medeze"]

    # actor config
    actor_name = "youtube-post"
    kwargs = {"date_filter": "year"}
    search_items = keywords

    # Run apify scraper
    scraper = ApifyScraper(apify_token, actor_name, **kwargs)
    results = await scraper.run(search_items)
    # raw
    write_json_file(results, "dev/apify_eda/youtube_posts.json")

    ## Transform
    # read raw

    # filter comment>0

    # remove duplicate

    # filter relate

    # output transform before table

    # write json


if __name__ == "__main__":
    # print(get_actor_config("facebook-post", max_posts=500, days_lookback=30))
    # asyncio.run(test_facebook_post())
    asyncio.run(test_facebook_post())
