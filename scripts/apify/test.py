import asyncio
from lib.scraper.apify import Apify<PERSON><PERSON>raper
from lib.common.config import SecretConfig
import json

apify_token = SecretConfig().get_apify_token()


def write_json_file(data: list[dict], filename: str) -> None:
    """Write data to a JSON file."""
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=4)


async def test_tiktok():
    keywords = ["medeze", "เมดีซ"]

    task_runner = ApifyScraper(
        apify_token=apify_token,
        actor_name="tiktok",
    )

    search_result = await task_runner.run(keywords)
    write_json_file(search_result, "dev/tiktok.json")


async def test_ig_comment():
    # ig comments

    search_items = [
        "https://www.instagram.com/p/CFQXrhbjJSd/,"
        "https://www.instagram.com/p/BeuFLfaDjZY/"
    ]

    task_runner = ApifyScraper(
        apify_token=apify_token,
        actor_name="instagram-comment",
    )

    search_result = await task_runner.run(search_items)
    write_json_file(search_result, "dev/ig-comment.json")


async def test_yt_post():
    keywords = ["medeze", "เมดีซ"]

    ###### youtube
    task_runner_search = ApifyScraper(
        apify_token=apify_token,
        actor_name="youtube",
    )
    search_result = await task_runner_search.run(keywords)
    print(f"Posts found: {len(search_result)}")

    # Remove duplicate posts based on post_id
    unique_posts = {post["id"]: post for post in search_result}
    print(f"Unique Posts found: {len(unique_posts)}")
    write_json_file(list(unique_posts.values()), "dev/yt_posts.json")

    # Get posts with comment_count > 0
    posts_comment = [
        post for post in unique_posts.values() if post.get("commentsCount", 0) > 0
    ]
    # Get links from posts_comment
    search_comment_links = [post.get("url", "") for post in posts_comment]
    for item in search_comment_links:
        print(item)

    task_runner_comment = ApifyScraper(
        apify_token=apify_token,
        actor_name="youtube-comment",
    )
    comment_result = await task_runner_comment.run(search_comment_links)
    write_json_file(comment_result, "dev/yt_comments.json")


async def test_fb_post():
    keywords = ["medeze", "เมดีซ"]
    #### facebook
    task_runner_search = ApifyScraper(
        apify_token=apify_token,
        actor_name="facebook-search",
    )
    search_result = await task_runner_search.run(keywords)
    print(f"Posts found: {len(search_result)}")

    # Remove duplicate posts based on post_id
    unique_posts = {post["post_id"]: post for post in search_result}
    print(f"Unique Posts found: {len(unique_posts)}")
    write_json_file(list(unique_posts.values()), "dev/fb_posts.json")

    # Get posts with comment_count > 0
    posts_comment = [
        post for post in unique_posts.values() if post.get("comments_count", 0) > 0
    ]
    # Get links from posts_comment
    search_comment_links = [post.get("url", "") for post in posts_comment]
    for item in search_comment_links:
        print(item)

    task_runner_comment = ApifyScraper(
        apify_token=apify_token,
        actor_name="facebook-comment",
    )
    comment_result = await task_runner_comment.run(search_comment_links)
    write_json_file(comment_result, "dev/fb_comments.json")


if __name__ == "__main__":
    asyncio.run(test_fb_post())
