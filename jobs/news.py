from jobs.base_job import BaseJob
from lib.scraper.news import NewsS<PERSON>raper
from lib.storage.database import Database
from lib.common.config import SecretConfig


class NewsJob(BaseJob):
    KEYWORD_TABLE = "data_scrape_keywords"

    def __init__(self, api_key: str, days_lookback: int = 3):

        self.api_key = api_key
        self.days_lookback = days_lookback
        self.scraper = NewsScraper(
            api_key=self.api_key, days_lookback=self.days_lookback
        )
        self.db = Database(SecretConfig().get_db_config())

    def _fetch_keywords(self) -> list[str]:
        sql = f"""
            SELECT keyword
            FROM {self.KEYWORD_TABLE}
        """
        results = self.db.execute(query=sql)
        return [row["keyword"] for row in results]

    def _save_to_database(self, news: list[dict]):
        pass

    def run(self):
        # fetch keyword from database
        keywords = self._fetch_keywords()

        news = self.scraper.run(keyword=keywords)

        # save to database
        self._save_to_database(news)


if __name__ == "__main__":
    api_key = SecretConfig().get_config(["serper", "api_key"])
    job = NewsJob(api_key=api_key, days_lookback=180)
    job.run()
