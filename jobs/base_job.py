from abc import ABC, abstractmethod

import os

RAW_DATA_PATH = "raw/{channel_name}/ingest_date={ingest_date}"
JOB_CONFIG_PATH = os.path.join(os.getcwd(), "jobs/job_config.yaml")


class BaseJob(ABC):
    """
    Base class for all jobs.
    """

    @abstractmethod
    def run(self):
        pass


class IngestJob(BaseJob):
    """
    Base class for all ingest jobs.
    Store raw data in gcs bucket.
    """

    @abstractmethod
    def extract(self):
        pass

    @abstractmethod
    def load(self):
        pass

    def run(self):
        pass


class TransformJob(BaseJob):
    """
    Base class for all transform jobs.
    """

    @abstractmethod
    def extract(self):
        pass

    @abstractmethod
    def transform(self):
        pass

    @abstractmethod
    def load(self):
        pass

    def run(self):
        pass
