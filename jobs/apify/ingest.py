from lib.common.logger import logger
from jobs.apify_job import ApifyPost<PERSON>ngestJob, ApifyCommentIngestJob
import asyncio
import argparse

if __name__ == "__main__":
    # Set up argument parser
    parser = argparse.ArgumentParser(
        description="Run ingestion job for a specific channel"
    )
    parser.add_argument(
        "channel_name", type=str, help="Name of the channel to ingest (required)"
    )

    # Parse arguments
    args = parser.parse_args()

    logger.info("Starting the job...")

    if "comment" in args.channel_name:
        logger.info(f"Run {args.channel_name} ingest job")
        job = ApifyCommentIngestJob(channel_name=args.channel_name)
        asyncio.run(job.run())
    else:
        logger.info(f"Run {args.channel_name} ingest job")
        job = ApifyPostIngestJob(channel_name=args.channel_name)
        asyncio.run(job.run())

    logger.info("Job completed successfully.")
