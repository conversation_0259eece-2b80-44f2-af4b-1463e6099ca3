from lib.common.logger import logger
from jobs.apify_job import ApifyTransformJob
import asyncio
import argparse

if __name__ == "__main__":
    # Set up argument parser
    parser = argparse.ArgumentParser(
        description="Run transform job for a specific channel"
    )
    parser.add_argument(
        "channel_name", type=str, help="Name of the channel to transform (required)"
    )

    # Parse arguments
    args = parser.parse_args()

    logger.info("Starting the job...")
    logger.info(f"Run {args.channel_name} transform job")

    job = ApifyTransformJob(channel_name=args.channel_name)
    job.run()

    logger.info("Job completed successfully.")
