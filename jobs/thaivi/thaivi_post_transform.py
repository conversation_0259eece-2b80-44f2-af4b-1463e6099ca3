from lib.common.logger import logger
from jobs.base_job import TransformJob, JOB_CONFIG_PATH, RAW_DATA_PATH
from lib.storage.gcs import GCS
from lib.storage.database import Database, DBConfig
from lib.common.config import SecretConfig, Config
from pydantic import BaseModel, HttpUrl
import pendulum
import datetime


class ThaiViPost(BaseModel):
    id: str
    timestamp: datetime.datetime
    post_number: str
    content: str
    author_name: str
    like_count: int
    comment_count: int
    url: HttpUrl

    @classmethod
    def parse(cls, data: dict):
        # Parse and convert timestamp to datetime
        data["timestamp"] = pendulum.parse(data["timestamp"])
        return cls.model_validate(data)


class ThaiViPostTransformJob(TransformJob):
    CHANNEL_NAME = "thaivi"

    def __init__(
        self,
        job_config_path: str = JOB_CONFIG_PATH,
        gcs_credentials_path: str = SecretConfig().get_datalake_bucket_credentials_path(),
        gcs_bucket: str = SecretConfig().get_datalake_bucket(),
        db_config: DBConfig = SecretConfig().get_db_config(),
    ):
        self.gcs = GCS(credentials_path=gcs_credentials_path, bucket_name=gcs_bucket)
        self.db = Database(config=db_config)

        target_table = Config(job_config_path).get_config(
            [self.CHANNEL_NAME, "post_target_table"]
        )
        if not isinstance(target_table, str):
            raise ValueError("Target table must be a string")
        self.target_table = target_table

    def extract(self):
        # Get the most recent ingest date
        ingest_date = pendulum.now().format("YYYY-MM-DD")
        path = RAW_DATA_PATH.format(
            channel_name=self.CHANNEL_NAME, ingest_date=ingest_date
        )
        gcs_path = f"{path}/posts.json"
        # Download from GCS
        data = self.gcs.read_json_file(gcs_path)

        return data

    def transform(self, data):
        return [ThaiViPost.parse(item) for item in data]

    def load(self, data):
        # Convert pydantic models to dictionaries
        data_dicts = [item.model_dump(mode="json") for item in data]

        # Upsert data into database
        rows_affected = self.db.upsert(
            table=self.target_table, data=data_dicts, unique_columns=["id"]
        )
        logger.info(
            f"Transformed data saved to database table: {self.target_table} ({rows_affected} rows affected)"
        )

    def run(self):
        data = self.extract()
        transformed_data = self.transform(data)
        self.load(transformed_data)


if __name__ == "__main__":
    logger.info("Starting the job...")
    logger.info("Run thaivi post transform job")
    job = ThaiViPostTransformJob()
    job.run()
    logger.info("Job completed successfully.")
