import asyncio
from datetime import datetime
from typing import List, Dict, Any, Union
import pendulum
import os

from jobs.base_job import IngestJ<PERSON>, Transform<PERSON>ob, RAW_DATA_PATH, JOB_CONFIG_PATH
from lib.scraper.apify.scraper import ApifyScraper
from lib.scraper.apify.transformer import get_transformer
from lib.common.logger import logger
from lib.storage.gcs import GCS
from lib.storage.database import Database, DBConfig
from lib.common.config import Config, SecretConfig

KEYWORDS_TABLE = "data_scrape_keywords"


class ApifyPostIngestJob(IngestJob):
    def __init__(
        self,
        channel_name: str,
        apify_token: str = SecretConfig().get_apify_token(),
        gcs_credentials_path: str = SecretConfig().get_datalake_bucket_credentials_path(),
        gcs_bucket: str = SecretConfig().get_datalake_bucket(),
        db_config: DBConfig = SecretConfig().get_db_config(),
        job_config_path: str = JOB_CONFIG_PATH,
    ):
        self.apify_token = apify_token
        self.channel_name = channel_name
        self.gcs = GCS(credentials_path=gcs_credentials_path, bucket_name=gcs_bucket)
        self.db = Database(config=db_config)
        self.apify_config = Config(job_config_path).get_config(
            [channel_name, "apify_config"]
        )
        logger.info(f"Loaded job config for {channel_name}: {self.apify_config}")

    def fetch_keywords(self) -> List[str]:
        """Fetch keywords from database for the specified channel."""
        results = self.db.read(table=KEYWORDS_TABLE)
        return [row["keyword"] for row in results]

    async def extract(self) -> List[dict] | None:
        """Fetch keywords from database and run apify scraper."""
        keywords = self.fetch_keywords()

        if not keywords:
            logger.warning(f"No keywords found for channel: {self.channel_name}")
            return None
        else:
            logger.info(f"Found {len(keywords)} keywords")
            logger.info(keywords)

        # Run apify scraper with job config
        scraper = ApifyScraper(
            apify_token=self.apify_token,
            actor_name=self.channel_name,
            **(
                {k: v for k, v in self.apify_config.items()}
                if isinstance(self.apify_config, dict)
                else {}
            ),
        )
        results = await scraper.run(keywords)
        return results

    def load(self, data: List[dict]):
        """Save data to GCS bucket with proper path structure."""
        ingest_date = pendulum.now().format("YYYY-MM-DD")
        path = RAW_DATA_PATH.format(
            channel_name=self.channel_name, ingest_date=ingest_date
        )
        gcs_path = f"{path}/data.json"

        # Upload to GCS
        self.gcs.upload_json_file(data=data, file_path=gcs_path)
        logger.info(f"Data saved to GCS: {gcs_path}")

    async def run(self):
        """Run the ingest job."""
        # Get keywords from database and run scraper
        results = await self.extract()
        if not results:
            logger.warning(f"No results found for channel: {self.channel_name}")
            return

        logger.info(f"Found {len(results)} results from scraper")

        # Save results to GCS
        if results:
            self.load(results)
        else:
            logger.warning("No results found from scraper")


class ApifyCommentIngestJob(IngestJob):
    def __init__(
        self,
        channel_name: str,
        apify_token: str = SecretConfig().get_apify_token(),
        gcs_credentials_path: str = SecretConfig().get_datalake_bucket_credentials_path(),
        gcs_bucket: str = SecretConfig().get_datalake_bucket(),
        db_config: DBConfig = SecretConfig().get_db_config(),
        job_config_path: str = JOB_CONFIG_PATH,
    ):
        self.apify_token = apify_token
        self.channel_name = channel_name
        self.gcs = GCS(credentials_path=gcs_credentials_path, bucket_name=gcs_bucket)
        self.db = Database(config=db_config)
        self.job_config = Config(job_config_path)
        self.apify_config = self.job_config.get_config([channel_name, "apify_config"])
        self.source_table = self.job_config.get_config([channel_name, "source_table"])
        self.days_lookback = self.job_config.get_config([channel_name, "days_lookback"])

    def get_search_items(self) -> List[str]:
        if self.channel_name == "twitter-comment":
            return self.fetch_conversation_ids()
        else:
            return self.fetch_post_links()

    def fetch_post_links(self) -> List[str]:
        """Fetch post links from database for the specified channel."""
        sql = f"""
            SELECT url
            FROM {self.source_table}
            WHERE comment_count > 0 AND timestamp >= (now() - interval '{self.days_lookback} days')
        """
        results = self.db.execute(query=sql)
        return [row["url"] for row in results]

    def fetch_conversation_ids(self) -> List[str]:
        """Fetch conversation ids from database for twitter."""
        sql = f"""
            SELECT id
            FROM {self.source_table}
            WHERE comment_count > 0 AND is_reply = false AND timestamp >= (now() - interval '{self.days_lookback} days')
        """
        results = self.db.execute(query=sql)
        return [row["id"] for row in results]

    async def extract(self) -> List[dict] | None:
        """Fetch post links from database and run apify scraper."""
        # Get post links from database
        search_items = self.get_search_items()

        if not search_items:
            logger.warning(f"No search items found for channel: {self.channel_name}")
            return None
        else:
            logger.info(f"Found {len(search_items)} search items")

        # Run apify scraper with job config
        scraper = ApifyScraper(
            apify_token=self.apify_token,
            actor_name=self.channel_name,
            **(
                {k: v for k, v in self.apify_config.items()}
                if isinstance(self.apify_config, dict)
                else {}
            ),
        )
        results = await scraper.run(search_items)
        return results

    def load(self, data: List[dict]):
        """Save data to GCS bucket with proper path structure."""
        ingest_date = pendulum.now().format("YYYY-MM-DD")
        path = RAW_DATA_PATH.format(
            channel_name=self.channel_name, ingest_date=ingest_date
        )
        gcs_path = f"{path}/data.json"

        # Upload to GCS
        self.gcs.upload_json_file(data=data, file_path=gcs_path)
        logger.info(f"Data saved to GCS: {gcs_path}")

    async def run(self):
        results = await self.extract()
        if not results:
            logger.warning(f"No results found for channel: {self.channel_name}")
            return

        logger.info(f"Found {len(results)} results from scraper")

        # Save results to GCS
        if results:
            self.load(results)
        else:
            logger.warning("No results found from scraper")


class ApifyTransformJob(TransformJob):
    def __init__(
        self,
        channel_name: str,
        gcs_credentials_path: str = SecretConfig().get_datalake_bucket_credentials_path(),
        gcs_bucket: str = SecretConfig().get_datalake_bucket(),
        db_config: DBConfig = SecretConfig().get_db_config(),
        job_config_path: str = JOB_CONFIG_PATH,
    ):
        self.channel_name = channel_name
        self.gcs = GCS(credentials_path=gcs_credentials_path, bucket_name=gcs_bucket)
        self.db = Database(config=db_config)
        self.output_transformer = get_transformer(self.channel_name)
        self.target_table = self.get_target_table(job_config_path)
        self.content_type = self.get_content_type()

    def get_content_type(self):
        if "comment" in self.channel_name:
            return "comment"
        else:
            return "post"

    def get_target_table(self, job_config_path: str) -> str:
        target_table = Config(job_config_path).get_config(
            [self.channel_name, "target_table"]
        )
        if target_table is None:
            raise ValueError(
                f"Target table not found in config for channel {self.channel_name}"
            )
        if not isinstance(target_table, str):
            raise ValueError("Target table must be a string")

        return target_table

    def extract(self) -> List[Dict[str, Any]]:
        """Read data from GCS bucket."""
        # Get the most recent ingest date
        ingest_date = pendulum.now().format("YYYY-MM-DD")
        gcs_path = f"raw/{self.channel_name}/ingest_date={ingest_date}/data.json"

        # Download from GCS
        data = self.gcs.read_json_file(gcs_path)
        # Ensure we return a list of dictionaries
        if isinstance(data, dict):
            return [data]
        return data or []  # Return an empty list if data is None

    def is_medeze_related(self, data: dict) -> bool:
        keywords = ["medeze", "เมดีซ"]
        content = data.get("content", "").lower()
        author = data.get("author_name", "").lower()
        url = data.get("url", "").lower()
        combined_text = f"{content} {author} {url}"
        return any(keyword in combined_text for keyword in keywords)

    def remove_duplicate(self, data: List[dict]) -> List[dict]:
        unique_items = {item["id"]: item for item in data}
        return list(unique_items.values())

    def transform(self, data: List[dict]) -> List[dict]:
        """Transform data using ApifyOutputTransformer."""
        # Use safe_transform and filter out None values
        parsed_data = [
            item
            for item in (self.output_transformer.safe_transform(item) for item in data)
            if item is not None
        ]

        # Remove duplicate
        unique_data = self.remove_duplicate(parsed_data)

        if self.content_type == "post":
            # Filter related
            results = [data for data in unique_data if self.is_medeze_related(data)]
            logger.info(f"Found {len(results)} related results")
        else:
            results = unique_data
            logger.info(f"Found {len(results)} results")

        return results

    def load(self, data: List[dict]):
        """Save transformed data to database."""
        table_name = str(self.target_table)
        # Upsert data into database
        # Using id as the unique column for upsert
        rows_affected = self.db.upsert(
            table=table_name, data=data, unique_columns=["id"]
        )
        logger.info(
            f"Transformed data saved to database table: {table_name} ({rows_affected} rows affected)"
        )

    def run(self):
        """Run the transform job."""
        # Extract data from GCS
        raw_data = self.extract()
        if not raw_data:
            logger.warning("No data found in GCS")
            return

        # Transform data
        transformed_data = self.transform(raw_data)

        # Save to database
        if transformed_data:
            self.load(transformed_data)
        else:
            logger.warning("No data to save after transformation")
