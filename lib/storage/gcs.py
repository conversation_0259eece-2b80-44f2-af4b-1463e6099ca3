import json
import os
from typing import Any, Dict, List, Union
from google.cloud import storage
from google.oauth2 import service_account
from lib.common.logger import logger
from lib.common.config import Config


class GCS:
    def __init__(self, credentials_path: str, bucket_name: str):
        self.credentials_path = credentials_path
        self.bucket_name = bucket_name
        self.client = self._get_client()
        self.bucket = self._get_bucket()

    def _get_client(self) -> storage.Client:
        """Initialize GCS client with credentials."""
        try:
            if not os.path.exists(self.credentials_path):
                raise FileNotFoundError(
                    f"GCS credentials file not found at {self.credentials_path}"
                )

            credentials = service_account.Credentials.from_service_account_file(
                self.credentials_path
            )
            return storage.Client(credentials=credentials)
        except Exception as e:
            logger.error(f"Failed to initialize GCS client: {e}")
            raise

    def _get_bucket(self) -> storage.Bucket:
        """Get the GCS bucket."""
        try:
            return self.client.bucket(self.bucket_name)
        except Exception as e:
            logger.error(f"Failed to get bucket {self.bucket_name}: {e}")
            raise

    def read_json_file(
        self, file_path: str, default_value=None
    ) -> Union[Dict[str, Any], List[Dict[str, Any]], None]:
        """Read JSON file from GCS.

        Args:
            file_path: Path to the file in GCS bucket
            default_value: Value to return if file not found (default: None)

        Returns:
            Parsed JSON content or default_value if file not found
        """
        try:
            blob = self.bucket.blob(file_path)
            if not blob.exists():
                logger.warning(f"File not found in GCS: {file_path}")
                return default_value
            content = blob.download_as_text()
            return json.loads(content)
        except Exception as e:
            logger.error(f"Failed to read JSON file {file_path} from GCS: {e}")
            raise

    def upload_json_file(
        self, data: Union[Dict[str, Any], List[Dict[str, Any]]], file_path: str
    ) -> str:
        """Upload JSON data to GCS."""
        try:
            blob = self.bucket.blob(file_path)
            blob.upload_from_string(
                json.dumps(data, ensure_ascii=False, indent=2),
                content_type="application/json",
            )
            logger.info(f"Successfully uploaded JSON data to {file_path}")
            return f"gs://{self.bucket_name}/{file_path}"
        except Exception as e:
            logger.error(f"Failed to upload JSON data to {file_path}: {e}")
            raise
