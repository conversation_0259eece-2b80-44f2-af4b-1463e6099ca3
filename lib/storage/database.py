import os
import psycopg2
from psycopg2.extras import RealDictCursor, execute_values
from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel
from lib.common.logger import logger


class DBConfig(BaseModel):
    host: str
    port: int
    dbname: str
    user: str
    password: str


class Database:
    _instance = None

    def __new__(cls, config: Optional[DBConfig] = None):
        if cls._instance is None:
            cls._instance = super(Database, cls).__new__(cls)
            cls._instance._conn = None
            cls._instance._config = config
        return cls._instance

    def __init__(self, config: Optional[DBConfig] = None):
        if config:
            self._config = config

    def connect(self):
        """Establish connection to PostgreSQL database."""
        if self._conn is None or self._conn.closed:
            try:
                self._conn = psycopg2.connect(
                    host=self._config.host,
                    port=self._config.port,
                    database=self._config.dbname,
                    user=self._config.user,
                    password=self._config.password,
                )
                logger.info(f"Connected to PostgreSQL database: {self._config.dbname}")
            except Exception as e:
                logger.error(f"Failed to connect to PostgreSQL: {e}")
                raise

    def disconnect(self):
        """Close the database connection."""
        if self._conn and not self._conn.closed:
            self._conn.close()
            self._conn = None
            logger.info("Disconnected from PostgreSQL database")

    def read(
        self,
        table: str,
        conditions: Optional[Dict[str, Any]] = None,
        fields: Optional[List[str]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Read data from a table and return as list of dictionaries.

        Args:
            table: Table name
            conditions: Optional dictionary of column-value pairs for WHERE clause
            fields: Optional list of fields to select (defaults to all fields)

        Returns:
            List of dictionaries representing rows
        """
        self.connect()

        try:
            with self._conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Construct SELECT statement
                select_fields = ", ".join(fields) if fields else "*"
                query = f"SELECT {select_fields} FROM {table}"

                # Add WHERE clause if conditions provided
                params = []
                if conditions:
                    where_clauses = []
                    for key, value in conditions.items():
                        where_clauses.append(f"{key} = %s")
                        params.append(value)

                    query += " WHERE " + " AND ".join(where_clauses)

                # Execute query
                cursor.execute(query, params)
                results = cursor.fetchall()

                return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Error reading from table {table}: {e}")
            self._conn.rollback()
            raise

    def insert(
        self, table: str, data: Union[Dict[str, Any], List[Dict[str, Any]]]
    ) -> int:
        """
        Insert data into a table.

        Args:
            table: Table name
            data: Dictionary or list of dictionaries to insert

        Returns:
            Number of rows inserted
        """
        self.connect()

        try:
            with self._conn.cursor() as cursor:
                if isinstance(data, dict):
                    # Single row insert
                    columns = list(data.keys())
                    placeholders = ", ".join(["%s"] * len(columns))
                    query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({placeholders})"
                    cursor.execute(query, [data[col] for col in columns])
                    rows_affected = cursor.rowcount
                else:
                    # Multi-row insert
                    if not data:
                        return 0

                    columns = list(data[0].keys())
                    query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES %s"
                    values = [[row[col] for col in columns] for row in data]
                    execute_values(cursor, query, values)
                    rows_affected = cursor.rowcount

                self._conn.commit()
                return rows_affected

        except Exception as e:
            logger.error(f"Error inserting into table {table}: {e}")
            self._conn.rollback()
            raise

    def upsert(
        self,
        table: str,
        data: Union[Dict[str, Any], List[Dict[str, Any]]],
        unique_columns: List[str],
    ) -> int:
        """
        Upsert data into a table (insert or update if exists).

        Args:
            table: Table name
            data: Dictionary or list of dictionaries to upsert
            unique_columns: List of column names that form the unique constraint

        Returns:
            Number of rows affected
        """
        self.connect()

        try:
            with self._conn.cursor() as cursor:
                if isinstance(data, dict):
                    data = [data]

                if not data:
                    return 0

                # Add updated_at to each row
                for row in data:
                    row["updated_at"] = "now()"

                columns = list(data[0].keys())

                # Construct the ON CONFLICT clause
                conflict_target = ", ".join(unique_columns)

                # Determine which columns to update (exclude unique columns)
                update_columns = [col for col in columns if col not in unique_columns]
                if not update_columns:
                    # If no columns to update, do nothing on conflict
                    update_action = "DO NOTHING"
                else:
                    # Create SET clause for updates
                    set_clause = ", ".join(
                        [f"{col} = EXCLUDED.{col}" for col in update_columns]
                    )
                    update_action = f"DO UPDATE SET {set_clause}"

                # Construct the full query
                query = f"""
                    INSERT INTO {table} ({', '.join(columns)})
                    VALUES %s
                    ON CONFLICT ({conflict_target}) {update_action}
                """

                # Prepare values
                values = [[row[col] for col in columns] for row in data]

                # Execute the query
                execute_values(cursor, query, values)
                rows_affected = cursor.rowcount

                self._conn.commit()
                return rows_affected

        except Exception as e:
            logger.error(f"Error upserting into table {table}: {e}")
            self._conn.rollback()
            raise

    def execute(self, query: str, params: Optional[List[Any]] = None) -> Any:
        """
        Execute a custom SQL query.

        Args:
            query: SQL query to execute
            params: Optional parameters for the query

        Returns:
            Query results or number of affected rows
        """
        self.connect()

        try:
            with self._conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, params or [])

                # Check if query is a SELECT
                if query.strip().upper().startswith("SELECT"):
                    results = cursor.fetchall()
                    return [dict(row) for row in results]
                else:
                    self._conn.commit()
                    return cursor.rowcount

        except Exception as e:
            logger.error(f"Error executing query: {e}")
            self._conn.rollback()
            raise
