import requests
from typing import Dict, Any, Optional
from urllib.parse import urljoin

from lib.common.logger import logger


class LLMService:
    def __init__(self, base_url: str):
        """
        Initialize the BackendService with base URL and optional API key.

        Args:
            base_url (str): The base URL of the backend service
            api_key (str, optional): API key for authentication
        """
        self.base_url = base_url.rstrip("/")
        self.session = requests.Session()

        self.session.headers.update(
            {"Content-Type": "application/json", "Accept": "application/json"}
        )

    def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Make an HTTP request to the backend service.

        Args:
            method (str): HTTP method (GET, POST, etc.)
            endpoint (str): API endpoint path
            data (dict, optional): Request body data
            params (dict, optional): URL query parameters

        Returns:
            dict: Response data

        Raises:
            requests.exceptions.RequestException: If the request fails
        """
        url = urljoin(self.base_url, endpoint)

        try:
            response = self.session.request(
                method=method, url=url, json=data, params=params
            )
            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed: {str(e)}")
            raise

    def get_data(
        self, endpoint: str, params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make a GET request to the backend service.

        Args:
            endpoint (str): API endpoint path
            params (dict, optional): URL query parameters

        Returns:
            dict: Response data
        """
        return self._make_request("GET", endpoint, params=params)

    def close(self):
        """
        Close the session and release resources.
        """
        self.session.close()
