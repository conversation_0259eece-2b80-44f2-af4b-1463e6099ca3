from abc import ABC, abstractmethod
from pydantic import BaseModel, HttpUrl, Field, ValidationError
from typing import Optional, List
import pendulum
import datetime
import re
from lib.common.logger import logger


class ApifyOutputTransformer(ABC):
    @abstractmethod
    def transform(self, data: dict) -> dict:
        """Transform the output data from the Apify actor."""
        pass

    def safe_transform(self, data: dict) -> Optional[dict]:
        """Safely transform data, returning None if validation fails."""
        try:
            return self.transform(data)
        except (Validation<PERSON><PERSON>r, ValueError, KeyError, TypeError) as e:
            logger.warning(f"Failed to transform data: {str(e)[:100]}...")
            return None


#############################
# Facebook
#############################


class FacebookReactions(BaseModel):
    angry: int = 0
    care: int = 0
    haha: int = 0
    like: int = 0
    love: int = 0
    sad: int = 0
    wow: int = 0


class FacebookAuthor(BaseModel):
    id: str
    name: str


class FacebookPost(BaseModel):
    id: str = Field(..., alias="post_id")
    url: HttpUrl
    content: Optional[str] = Field(..., alias="message")
    timestamp: datetime.datetime = Field(..., alias="timestamp")
    hashtags: list
    comment_count: int = Field(0, alias="comments_count")
    # comment_count: int = Field(0, alias = "commentsCount")
    reaction_count: int = Field(0, alias="reactions_count")
    engagement_count: int = 0
    reshare_count: int = 0
    reactions: FacebookReactions
    author: FacebookAuthor
    # image: Optional[dict, str]

    @classmethod
    def parse(cls, data: dict):
        # Parse and convert timestamp to datetime
        if "message" in data:
            if data["message"] is None:
                data["message"] = "[No text]"
            else:
                data["message"] = data["message"].replace("\n", " ")
        data["hashtags"] = re.findall(r"#\S+", data["message"])
        data["timestamp"] = pendulum.from_timestamp(data["timestamp"])
        data["engagement_count"] = (
            data["reactions_count"] + data["comments_count"] + data["reshare_count"]
        )
        return cls.model_validate(data)


class FacebookComment(BaseModel):
    id: str
    timestamp: datetime.datetime
    parent_comment_id: Optional[str] = Field(None, alias="replyToCommentId")
    content: Optional[str] = Field(None, alias="text")
    comment_url: HttpUrl = Field(..., alias="commentUrl")
    like_count: int = Field(..., alias="likesCount")
    comment_count: Optional[int] = Field(0, alias="commentsCount")
    engagement_count: int = 0
    author_name: str = Field(..., alias="profileName")
    author_id: str = Field(..., alias="profileId")
    threading_depth: int = Field(..., alias="threadingDepth")
    input_url: HttpUrl = Field(..., alias="inputUrl")
    post_id: str = Field(..., alias="facebookId")

    @classmethod
    def parse(cls, data: dict):
        # Parse and convert timestamp to datetime
        if "text" in data:
            data["text"] = data["text"].replace("\n", " ")
        data["timestamp"] = pendulum.parse(data["date"])

        data["likesCount"] = int(data["likesCount"])

        if "commentsCount" in data:
            data["engagement_count"] = data["likesCount"] + data["commentsCount"]
        else:
            data["engagement_count"] = data["likesCount"]
        return cls.model_validate(data)


class FacebookPostOutputTransformer(ApifyOutputTransformer):
    def transform(self, data: dict) -> dict:
        """Transform the output data from the Apify actor."""
        post = FacebookPost.parse(data)
        parsed_data = {
            "id": post.id,
            "timestamp": post.timestamp,
            "content": post.content,
            "url": str(post.url),
            "author_name": post.author.name,
            "author_id": post.author.id,
            "hashtags": post.hashtags,
            "like_count": post.reactions.like,
            "love_count": post.reactions.love,
            "wow_count": post.reactions.wow,
            "sad_count": post.reactions.sad,
            "angry_count": post.reactions.angry,
            "haha_count": post.reactions.haha,
            "comment_count": post.comment_count,
            "reaction_count": post.reaction_count,
            "engagement_count": post.engagement_count,
            "share_count": post.reshare_count,
        }
        return parsed_data


class FacebookCommentOutputTransformer(ApifyOutputTransformer):
    def transform(self, data: dict) -> dict:
        """Transform the output data from the Apify actor."""
        comment = FacebookComment.parse(data)
        parsed_data = {
            "id": comment.id,
            "timestamp": comment.timestamp,
            "parent_comment_id": comment.parent_comment_id,
            "content": comment.content,
            "url": str(comment.comment_url),
            "like_count": int(comment.like_count),
            "reply_count": comment.comment_count,
            "engagement_count": comment.engagement_count,
            "author_name": comment.author_name,
            "author_id": comment.author_id,
            "threading_depth": comment.threading_depth,
            "post_id": comment.post_id,
        }
        return parsed_data


#############################
# Instagram
#############################


class InstagramVideo(BaseModel):
    id: str
    url: HttpUrl
    width: int
    height: int
    duration: float
    play_count: Optional[int] = Field(None, alias="playCount")


class InstagramImage(BaseModel):
    url: HttpUrl
    width: int
    height: int


class InstagramOwner(BaseModel):
    id: str
    username: str
    full_name: Optional[str] = Field(None, alias="fullName")
    profile_pic_url: Optional[HttpUrl] = Field(None, alias="profilePicUrl")
    is_private: bool = Field(..., alias="isPrivate")
    is_verified: bool = Field(..., alias="isVerified")


class InstagramPost(BaseModel):
    id: str
    timestamp: datetime.datetime
    content: Optional[str] = Field(..., alias="caption")
    hashtags: list
    url: HttpUrl
    like_count: int = Field(..., alias="likeCount")
    comment_count: int = Field(..., alias="commentCount")
    engagement_count: int = 0
    owner: InstagramOwner
    video: Optional[InstagramVideo] = None
    image: Optional[InstagramImage] = None

    @classmethod
    def parse(cls, data: dict):

        # Extract post_id from URL, the one from scraper cannot map with comment
        url_pattern = r"https://www\.instagram\.com/p/([^/]+)/?"
        match = re.search(url_pattern, data["url"])
        if match:
            data["id"] = match.group(1)
        # Parse and convert timestamp to datetime
        if "caption" in data:
            data["caption"] = data["caption"].replace("\n", " ")
        data["timestamp"] = pendulum.parse(data["createdAt"])

        # Use regular expression to find hashtags, keeping the #
        hashtags = re.findall(r"#([^#\s]+)", data["caption"])
        hashtags = ["#" + tag for tag in hashtags]

        data["hashtags"] = hashtags
        data["engagement_count"] = data["likeCount"] + data["commentCount"]
        return cls.model_validate(data)


class InstagramUser(BaseModel):
    id: int
    username: str
    image: Optional[HttpUrl] = Field(None, alias="image")
    is_verified: bool = Field(..., alias="isVerified")


class InstagramComment(BaseModel):
    id: str
    timestamp: datetime.datetime = Field(..., alias="createdAt")
    content: str = Field(..., alias="message")
    like_count: int = Field(..., alias="likeCount")
    engagement_count: int = 0
    author: InstagramUser = Field(..., alias="user")
    post_id: str = Field(..., alias="postId")
    is_ranked: bool = Field(..., alias="isRanked")

    @classmethod
    def parse(cls, data: dict):
        # Parse and convert timestamp to datetime
        if "message" in data:
            data["message"] = data["message"].replace("\n", " ")
        data["createdAt"] = pendulum.from_timestamp(data["createdAt"])
        data["engagement_count"] = data["likeCount"]
        data["post_id"] = data["postId"]
        return cls.model_validate(data)


class InstagramPostOutputTransformer(ApifyOutputTransformer):
    def transform(self, data: dict) -> dict:
        """Transform the output data from the Apify actor."""
        post = InstagramPost.parse(data)
        parsed_data = {
            "id": post.id,
            "timestamp": post.timestamp,
            "content": post.content,
            "url": str(post.url),
            "hashtags": post.hashtags,
            "like_count": post.like_count,
            "comment_count": post.comment_count,
            "author_name": post.owner.username,
            "author_id": post.owner.id,
            "engagement_count": post.engagement_count,
            "video_url": str(post.video.url) if post.video else None,
            "image_url": str(post.image.url) if post.image else None,
        }
        return parsed_data


class InstagramCommentOutputTransformer(ApifyOutputTransformer):
    def transform(self, data: dict) -> dict:
        """Transform the output data from the Apify actor."""
        comment = InstagramComment.parse(data)
        parsed_data = {
            "id": comment.id,
            "timestamp": comment.timestamp,
            "content": comment.content,
            "like_count": comment.like_count,
            "engagement_count": comment.engagement_count,
            "author_name": comment.author.username,
            "author_id": comment.author.id,
            "post_id": comment.post_id,
        }
        return parsed_data


#############################
# Youtube
#############################


class YoutubeDescriptionLink(BaseModel):
    url: HttpUrl
    text: str


class YoutubePost(BaseModel):
    title: str
    video_type: str = Field(..., alias="type")
    id: str
    url: HttpUrl
    view_count: int = Field(0, alias="viewCount")
    timestamp: datetime.datetime = Field(..., alias="date")
    likes: int
    author_name: str = Field(..., alias="channelName")
    author_username: Optional[str] = Field(None, alias="channelUsername")
    author_id: str = Field(..., alias="channelId")
    comment_count: int = Field(0, alias="commentsCount")
    engagement_count: int = 0
    content: str = Field(..., alias="text")
    hashtags: List[str]

    @classmethod
    def parse(cls, data: dict):
        # Parse data for output
        if "text" in data:
            data["text"] = data["text"].replace("\n", " ")
        data["timestamp"] = pendulum.parse(data["date"])
        data["engagement_count"] = data["likes"] + data["commentsCount"]
        return cls.model_validate(data)


class YouTubeComment(BaseModel):
    cid: str
    reply_to_cid: Optional[str] = Field(..., alias="replyToCid")
    type: str
    published_time_text: str = Field(..., alias="publishedTimeText")
    timestamp: datetime.datetime
    content: str = Field(..., alias="comment")
    author: str
    reply_count: int = Field(..., alias="replyCount")
    vote_count: int = Field(..., alias="voteCount")
    video_id: str = Field(..., alias="videoId")
    engagement_count: int = 0

    @classmethod
    def parse(cls, data: dict):
        """Convert published_time_text to DateTime object and parse data for output"""
        # clean text
        if "comment" in data:
            data["comment"] = data["comment"].replace("\n", " ")
        # Add engagement_count key
        data["engagement_count"] = data["voteCount"] + data["replyCount"]

        # Published_time_text to DateTime object
        now = pendulum.now("UTC")
        match = re.match(
            r"(\d+)\s+(seconds?|minutes?|hours?|days?|weeks?|months?|years?)\s+ago",
            data["publishedTimeText"].lower(),
        )

        if not match:
            raise ValueError(f"Unsupported time format: {data['publishedTimeText']}")

        unit = match[2]  # Unit for parsing to subract method
        value = int(match[1])  # Value for parsing to subract method

        if unit[-1:] != "s":
            unit += "s"

        data["timestamp"] = (
            now.subtract(**{unit: value}).replace(microsecond=0).in_timezone("UTC")
        )

        # extract post_id from pageUrl
        return cls.model_validate(data)


class YoutubePostOutputTransformer(ApifyOutputTransformer):
    def transform(self, data: dict) -> dict:
        """Transform the output data from the Apify actor."""
        post = YoutubePost.parse(data)
        parsed_data = {
            "id": post.id,
            "timestamp": post.timestamp,
            "url": str(post.url),
            "title": post.title,
            "content": post.content,
            "hashtags": post.hashtags,
            "like_count": post.likes,
            "comment_count": post.comment_count,
            "view_count": post.view_count,
            "video_type": post.video_type,
            "author_name": post.author_name,
            "author_id": post.author_id,
            "author_username": post.author_username,
            "engagement_count": post.engagement_count,
        }
        return parsed_data


class YoutubeCommentOutputTransformer(ApifyOutputTransformer):
    def transform(self, data: dict) -> dict:
        """Transform the output data from the Apify actor."""
        comment = YouTubeComment.parse(data)
        parsed_data = {
            "id": comment.cid,
            "timestamp": comment.timestamp,
            "type": comment.type,
            "parent_comment_id": comment.reply_to_cid,
            "content": comment.content,
            "like_count": comment.vote_count,
            "reply_count": comment.reply_count,
            "engagement_count": comment.engagement_count,
            "author_name": comment.author,
            "post_id": comment.video_id,
        }
        return parsed_data


#############################
# Twitter
#############################


class TwitterAuthor(BaseModel):
    user_name: str = Field(..., alias="userName")
    id: str


class TwitterHashtags(BaseModel):
    text: Optional[str]


class TwitterEntities(BaseModel):
    hashtags: list[TwitterHashtags]


class TwitterPost(BaseModel):
    id: str
    url: HttpUrl
    created_at: datetime.datetime = Field(..., alias="createdAt")
    content: str = Field(..., alias="fullText")
    retweet_count: int = Field(0, alias="retweetCount")
    reply_count: int = Field(0, alias="replyCount")
    like_count: int = Field(0, alias="likeCount")
    quote_count: int = Field(0, alias="quoteCount")
    view_count: int = Field(0, alias="viewCount")
    bookmark_count: int = Field(0, alias="bookmarkCount")
    engagement_count: int = 0
    author: TwitterAuthor
    hashtags: Optional[TwitterEntities] = Field(..., alias="entities")
    is_reply: bool = Field(..., alias="isReply")
    is_quote: bool = Field(..., alias="isQuote")
    is_retweet: bool = Field(..., alias="isRetweet")
    lang: str

    @classmethod
    def parse(cls, data: dict):
        # Parse and convert timestamp to datetime
        if "fullText" in data:
            data["fullText"] = data["fullText"].replace("\n", " ")
        data["createdAt"] = pendulum.from_format(
            data["createdAt"], "ddd MMM DD HH:mm:ss Z YYYY", tz="UTC"
        )
        data["engagement_count"] = (
            data["likeCount"]
            + data["replyCount"]
            + data["retweetCount"]
            + data["quoteCount"]
            + data["bookmarkCount"]
        )
        return cls.model_validate(data)


class TwitterComment(BaseModel):
    id: str
    url: HttpUrl
    created_at: datetime.datetime = Field(..., alias="createdAt")
    content: str = Field(..., alias="fullText")
    retweet_count: int = Field(0, alias="retweetCount")
    reply_count: int = Field(0, alias="replyCount")
    like_count: int = Field(0, alias="likeCount")
    quote_count: int = Field(0, alias="quoteCount")
    view_count: int = Field(0, alias="viewCount")
    bookmark_count: int = Field(0, alias="bookmarkCount")
    engagement_count: int = 0
    author: TwitterAuthor
    hashtags: Optional[TwitterEntities] = Field(..., alias="entities")
    is_reply: bool = Field(..., alias="isReply")
    is_quote: bool = Field(..., alias="isQuote")
    is_retweet: bool = Field(..., alias="isRetweet")
    lang: str
    post_id: str = Field(..., alias="conversationId")
    parent_comment_id: str = Field(..., alias="inReplyToId")

    @classmethod
    def parse(cls, data: dict):
        # Parse and convert timestamp to datetime
        if "fullText" in data:
            data["fullText"] = data["fullText"].replace("\n", " ")
        data["createdAt"] = pendulum.from_format(
            data["createdAt"], "ddd MMM DD HH:mm:ss Z YYYY", tz="UTC"
        )
        data["engagement_count"] = (
            data["likeCount"]
            + data["replyCount"]
            + data["retweetCount"]
            + data["quoteCount"]
            + data["bookmarkCount"]
        )
        return cls.model_validate(data)


class TwitterPostOutputTransformer(ApifyOutputTransformer):
    def transform(self, data: dict) -> dict:
        """Transform the output data from the Apify actor."""
        post = TwitterPost.parse(data)
        parsed_data = {
            "id": post.id,
            "timestamp": post.created_at,
            "content": post.content,
            "hashtags": (
                [
                    "#" + tag.text
                    for tag in post.hashtags.hashtags
                    if tag.text is not None
                ]
                if post.hashtags is not None
                else []
            ),
            "url": str(post.url),
            "author_name": post.author.user_name,  # assuming FacebookAuthor model has a `name` field
            "author_id": post.author.id,
            "like_count": post.like_count,
            "comment_count": post.reply_count,
            "retweet_count": post.retweet_count,
            "quote_count": post.quote_count,
            "view_count": post.view_count,
            "bookmark_count": post.bookmark_count,
            "engagement_count": post.engagement_count,
            "is_reply": post.is_reply,
            "is_quote": post.is_quote,
            "is_retweet": post.is_retweet,
            "lang": post.lang,
        }
        return parsed_data


class TwitterCommentOutputTransformer(ApifyOutputTransformer):
    def transform(self, data: dict) -> dict:
        """Transform the output data from the Apify actor."""
        post = TwitterComment.parse(data)
        parsed_data = {
            "id": post.id,
            "timestamp": post.created_at,
            "content": post.content,
            "hashtags": (
                [
                    "#" + tag.text
                    for tag in post.hashtags.hashtags
                    if tag.text is not None
                ]
                if post.hashtags is not None
                else []
            ),
            "url": str(post.url),
            "author_name": post.author.user_name,
            "author_id": post.author.id,
            "like_count": post.like_count,
            "comment_count": post.reply_count,
            "retweet_count": post.retweet_count,
            "quote_count": post.quote_count,
            "view_count": post.view_count,
            "bookmark_count": post.bookmark_count,
            "engagement_count": post.engagement_count,
            "post_id": post.post_id,
            "lang": post.lang,
            "parent_comment_id": (
                post.parent_comment_id
                if post.parent_comment_id != post.post_id
                else None
            ),
        }
        return parsed_data


#############################
# Tiktok
#############################


class TiktokChannel(BaseModel):
    name: str
    username: str
    id: str


class TiktokPost(BaseModel):
    id: str
    content: str = Field(..., alias="title")
    url: HttpUrl = Field(..., alias="postPage")
    views: int = 0
    likes: int = 0
    comments: int = 0
    shares: int = 0
    bookmarks: int = 0
    hashtags: List[str]
    channel: TiktokChannel
    uploaded_at_formatted: datetime.datetime = Field(..., alias="timestamp")
    engagement_count: int = 0

    @classmethod
    def parse(cls, data: dict):
        # Parse and convert timestamp to datetime
        if "title" in data:
            data["title"] = data["title"].replace("\n", " ")
        data["timestamp"] = pendulum.from_timestamp(data["uploadedAt"])
        data["engagement_count"] = data["likes"] + data["comments"] + data["shares"]
        return cls.model_validate(data)


class TikTokPostOutputTransformer(ApifyOutputTransformer):
    def transform(self, data: dict) -> dict:
        """Transform the output data from the Apify actor."""
        post = TiktokPost.parse(data)
        parsed_data = {
            "id": post.id,
            "timestamp": post.uploaded_at_formatted,
            "content": post.content,
            "url": str(post.url),
            "hashtags": post.hashtags,
            "author_name": post.channel.name,
            "author_username": post.channel.username,
            "author_id": post.channel.id,
            "like_count": post.likes,
            "comment_count": post.comments,
            "view_count": post.views,
            "share_count": post.shares,
            "bookmark_count": post.bookmarks,
            "engagement_count": post.engagement_count,
        }
        return parsed_data


class TiktokCommentUser(BaseModel):
    id: str
    username: str


class TiktokComment(BaseModel):
    id: str
    timestamp: datetime.datetime
    content: str = Field(..., alias="text")
    like_count: int = Field(0, alias="likeCount")
    reply_count: Optional[int] = Field(0, alias="replyCount")
    Author: TiktokCommentUser = Field(..., alias="user")
    search_term: str
    engagement_count: int = 0
    post_id: str = Field(..., alias="awemeId")

    @classmethod
    def parse(cls, data: dict):
        # Parse and convert timestamp to datetime
        if "text" in data:
            data["text"] = data["text"].replace("\n", " ")
        data["timestamp"] = pendulum.parse(data["createdAt"])

        if "replyCount" in data:
            data["engagement_count"] = data["likeCount"] + data["replyCount"]
        else:
            data["engagement_count"] = data["likeCount"]

        return cls.model_validate(data)


class TikTokCommentOutputTransformer(ApifyOutputTransformer):
    def transform(self, data: dict) -> dict:
        """Transform the output data from the Apify actor."""
        comment = TiktokComment.parse(data)
        parsed_data = {
            "id": comment.id,
            "timestamp": comment.timestamp,
            "content": comment.content,
            "like_count": comment.like_count,
            "reply_count": comment.reply_count,
            "author_name": comment.Author.username,
            "author_id": comment.Author.id,
            "engagement_count": comment.engagement_count,
            "post_id": comment.post_id,
        }
        return parsed_data


def get_transformer(actor_name: str) -> ApifyOutputTransformer:
    if actor_name == "facebook-post":
        return FacebookPostOutputTransformer()
    elif actor_name == "facebook-comment":
        return FacebookCommentOutputTransformer()
    elif actor_name == "instagram-post":
        return InstagramPostOutputTransformer()
    elif actor_name == "instagram-comment":
        return InstagramCommentOutputTransformer()
    elif actor_name == "youtube-post":
        return YoutubePostOutputTransformer()
    elif actor_name == "youtube-comment":
        return YoutubeCommentOutputTransformer()
    elif actor_name == "twitter-post":
        return TwitterPostOutputTransformer()
    elif actor_name == "twitter-comment":
        return TwitterCommentOutputTransformer()
    elif actor_name == "tiktok-post":
        return TikTokPostOutputTransformer()
    elif actor_name == "tiktok-comment":
        return TikTokCommentOutputTransformer()
    else:
        raise ValueError(f"Invalid actor name: {actor_name}")
