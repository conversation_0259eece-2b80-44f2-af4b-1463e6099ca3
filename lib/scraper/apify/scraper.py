import asyncio
import json
import uuid
from typing import Any, Dict, List

from apify_client import ApifyClientAsync
from apify_client.clients.resource_clients import TaskClientAsync

from lib.scraper.apify.config import get_actor_config
from lib.common.logger import logger


class ApifyScraper:
    def __init__(
        self,
        apify_token: str,
        actor_name: str,
        **kwargs: Any,
    ):
        self.apify_client = self._init_client(apify_token)
        self.actor_name = actor_name
        (
            self.actor_id,
            self.memory_mbytes,
            self.input_builder,
        ) = get_actor_config(self.actor_name, **kwargs)

    def _init_client(self, token: str) -> ApifyClientAsync:
        return ApifyClientAsync(token=token)

    async def create_and_run_task(self, search_term: str, index: int) -> list:
        logger.info(f"Creating task for {search_term}")

        # Create a copy of task_input and update the query
        task_input = self.input_builder.build(search_term)

        # Create task placeholder
        tasks_client = self.apify_client.tasks()
        apify_task = await tasks_client.create(
            name=f"{self.actor_name}-{uuid.uuid4()}",
            actor_id=self.actor_id,
            task_input=task_input,
        )

        # Run the task
        task_id = apify_task["id"]
        task_name = apify_task["name"]
        task_client = self.apify_client.task(task_id)
        logger.info(f"Running task: {task_name}")
        call_result = await task_client.call()
        logger.info(f"Finish task: {task_name}")
        if call_result is None:
            return []

        # Delete task on apify after finish
        await task_client.delete()

        # Get dataset
        dataset_client = self.apify_client.dataset(call_result["defaultDatasetId"])
        list_items_result = await dataset_client.list_items()
        result = list_items_result.items

        logger.info(f"Found {len(result)} results for {search_term}")

        for item in result:
            item["search_term"] = search_term

        return result

    async def run(self, search_items: List[str]) -> list:
        # Create search terms depend on input mode
        logger.info(f"Running {self.actor_name} with keywords: {search_items}")
        search_terms = self.input_builder.create_search_term(search_items)
        tasks = [
            self.create_and_run_task(search_term, index)
            for index, search_term in enumerate(search_terms)
        ]
        results = await asyncio.gather(*tasks)
        return [item for sublist in results for item in sublist]
