from playwright.sync_api import sync_playwright
from lib.common.logger import logger


class MedezePricePerformanceScraper:

    def run(self):
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.goto(
                "https://www.set.or.th/th/market/product/stock/quote/MEDEZE/factsheet"
            )
            card_history_div = page.locator("div.card-history")

            # Find the table with dynamic ID using a more general selector
            table = card_history_div.locator("table[id^='table-']").last

            # Extract data from the table
            data = dict()

            # Get all rows except header
            rows = table.locator("tbody tr")
            row_count = rows.count()

            for i in range(row_count):
                row = rows.nth(i)

                # Get label from first column
                label = row.locator("td:nth-child(1)").text_content().strip()

                # Get values from other columns
                securities = row.locator("td:nth-child(2)").text_content().strip()
                category = row.locator("td:nth-child(3)").text_content().strip()
                market = row.locator("td:nth-child(4)").text_content().strip()

                data[label] = {
                    "medeze": securities,
                    "helth": category,
                    "set": market,
                }

            browser.close()
            return data


# for debugging
if __name__ == "__main__":
    scraper = MedezePricePerformanceScraper()
    data = scraper.run()
    logger.info(data)
