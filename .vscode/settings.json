{
    "[python]": {
        "editor.defaultFormatter": "ms-python.black-formatter",
        "editor.tabSize": 4,
        "editor.insertSpaces": true
    },
    "editor.formatOnSave": true,
    "python.analysis.typeCheckingMode": "basic",
    "python.analysis.diagnosticSeverityOverrides": {
        "reportGeneralTypeIssues": "warning",
        "reportPrivateImportUsage": "warning",
        "reportOptionalMemberAccess": "warning"
    },
    "autoDocstring.docstringFormat": "google",
    "python.testing.pytestArgs": [
        "tests"
    ],
    "python.testing.unittestEnabled": false,
    "python.testing.pytestEnabled": true,
}
