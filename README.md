# Medeze Data Jobs

A Python-based data processing and job management system for Medeze Project.

## Prerequisites

- Python 3.12 or higher
- [uv](https://github.com/astral-sh/uv) package manager

## Setup

1. Initialize virtual environment and install dependencies using uv:
```bash
uv sync --locked
```

2. Copy the example configuration file:
```bash
cp config.yaml.example config.yaml
```

3. Update the `config.yaml` file with your specific configuration settings.

## Project Structure

```
medeze-data-jobs/
├── jobs/           # Data processing jobs
├── lib/            # Core library code
├── tests/          # Test suite
├── terraform/      # CI/CD jobs to Cloud Run Jobs and Workflow
├── config.yaml     # Configuration file
└── pyproject.toml  # Project dependencies
```

## Development


### Code Style

This project uses:
- Black for code formatting
- Pre-commit hooks for code quality checks

To set up pre-commit hooks:
```bash
pre-commit install
```

## Docker Support

The project includes a Dockerfile for containerized deployment. Build the image using:
