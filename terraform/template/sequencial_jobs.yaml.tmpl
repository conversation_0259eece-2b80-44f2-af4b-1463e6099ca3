main:
  params: [args]
  steps:
    - init:
        assign:
          - PROJECT_ID: $${sys.get_env("GOOGLE_CLOUD_PROJECT_ID")}
%{ for job in tf_jobs ~}
    - ${job.name}:
        call: execute_cloud_run_job
        args:
            project_id: $${PROJECT_ID}
            workflow_name: ${tf_workflow_name}
            job_name: ${job.name}
            job_location: ${job.location}
        result: OUTPUT_VARIABLE
%{ endfor ~}
    - done:
        return: "Workflow completed successfully."

# -----------------------------------------------
# Reusable subworkflow to run  Cloud Run Job
# -----------------------------------------------
execute_cloud_run_job:
  params: [project_id, workflow_name,job_name, job_location]
  steps:
    - run_job:
        try:
          call: googleapis.run.v1.namespaces.jobs.run
          args:
            name: $${"namespaces/" + project_id + "/jobs/" + job_name}
            location: $${job_location}
          result: job_result
        except:
          as: e
          steps:
            - notify_error:
                call: googleapis.workflowexecutions.v1.projects.locations.workflows.executions.run
                args:
                  workflow_id: ${tf_discord_workflow_id}
                  argument:
                    workflow_name: ${tf_workflow_name}
                    job_name: $${job_name}
            - raiseError:
                  raise: $${e}
