main:
  params: [args]
  steps:
    - init:
        assign:
          - WORKFLOW_NAME: ${args.workflow_name}
          - JOB_NAME: ${args.job_name}
          - PROJECT_ID: ${sys.get_env("GOOGLE_CLOUD_PROJECT_ID")}
    - send_discord_notification:
        call: http.post
        args:
          url: ${googleapis.secretmanager.v1.projects.secrets.versions.accessString("discord-webhook-url", "latest", PROJECT_ID)}
          body:
            embeds:
              - title: ${WORKFLOW_NAME + " - " + JOB_NAME + " failed!"}
                color: 15548997
                fields:
                  - name: "Environment"
                    value: ${PROJECT_ID}
                  - name: "Workflow"
                    value: ${WORKFLOW_NAME}
                  - name: "Job"
                    value: ${JOB_NAME}

        result: discord_response
