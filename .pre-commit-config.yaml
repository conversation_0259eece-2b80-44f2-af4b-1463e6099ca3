repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files

-   repo: https://github.com/psf/black
    rev: 25.1.0
    hooks:
    -   id: black
        language_version: python3.12

# -   repo: local
#     hooks:
#     -   id: pytest-check
#         name: pytest-check
#         entry: pytest
#         language: system
#         pass_filenames: false
#         always_run: true
