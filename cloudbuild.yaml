steps:
  - id: "clone-config-yaml"
    name: "gcr.io/cloud-builders/gcloud"
    entrypoint: "bash"
    args:
      - "-c"
      - |
        gcloud secrets versions access latest \
          --secret="${_CONFIG_SECRET}" \
          --format='get(payload.data)' | tr '_-' '/+' | base64 -d >  ./config.yaml

  - id: "clone-service-account"
    name: "gcr.io/cloud-builders/gcloud"
    entrypoint: "bash"
    args:
      - "-c"
      - |
        gcloud secrets versions access latest \
          --secret="${_SERVICE_ACCOUNT_SECRET}" \
          --format='get(payload.data)' | tr '_-' '/+' | base64 -d >  ./service_account.json

  # Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_IMAGE_NAME}:${_TAG}'
      - '.'
    id: 'build-image'

  # Push the Docker image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_IMAGE_NAME}:${_TAG}'
    id: 'push-image'

  # Tag the image as 'latest' as well
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'tag'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_IMAGE_NAME}:${_TAG}'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_IMAGE_NAME}:latest'
    id: 'tag-latest'

  # Push the 'latest' tag
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_IMAGE_NAME}:latest'
    id: 'push-latest'

  # Create backend.tf from template
  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
      - '-c'
      - 'sed "s/REPLACE/${PROJECT_ID}/g" terraform/backend.tf.template > terraform/backend.tf'
    id: 'create-backend'

  # Terraform init
  - name: 'hashicorp/terraform:1.7.0'
    args:
      - 'init'
    dir: 'terraform'
    id: 'tf-init'
    waitFor: ['create-backend']

  # Terraform plan
  - name: 'hashicorp/terraform:1.7.0'
    args:
      - 'plan'
      - '-var=project_id=${PROJECT_ID}'
      - '-var=region=${_REGION}'
      - '-var=container_image=${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_IMAGE_NAME}:${_TAG}'
      - '-out=tfplan'
    dir: 'terraform'
    id: 'tf-plan'

  # Terraform apply
  - name: 'hashicorp/terraform:1.7.0'
    args:
      - 'apply'
      - '-auto-approve'
      - 'tfplan'
    dir: 'terraform'
    id: 'tf-apply'

# Define the images that will be stored in Artifact Registry
images:
  - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_IMAGE_NAME}:${_TAG}'
  - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_IMAGE_NAME}:latest'

# Substitution variables with default values
substitutions:
  _REGION: 'asia-southeast1'
  _REPOSITORY: 'medeze-data-jobs'
  _IMAGE_NAME: 'medeze-data-jobs'
  _TAG: '${SHORT_SHA}'
  _CONFIG_SECRET: 'medeze-data-jobs-creds'
  _SERVICE_ACCOUNT_SECRET: 'sa-key-medeze-data-jobs-sa'

# Set a timeout for the build
timeout: '1200s'

# Use a specific service account for the build
options:
  logging: CLOUD_LOGGING_ONLY
